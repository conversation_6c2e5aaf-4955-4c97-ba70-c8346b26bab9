<?php
session_start();

// Simple authentication (in production, use proper authentication)
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

include 'config.php';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'create_collection':
                $name = mysqli_real_escape_string($conn, $_POST['collection_name']);
                $description = mysqli_real_escape_string($conn, $_POST['collection_description']);
                
                $sql = "INSERT INTO collections (name, description, created_at) VALUES ('$name', '$description', NOW())";
                if (mysqli_query($conn, $sql)) {
                    $success = "Collection created successfully!";
                } else {
                    $error = "Error creating collection: " . mysqli_error($conn);
                }
                break;
                
            case 'upload_design':
                $collection_id = (int)$_POST['collection_id'];
                $design_name = mysqli_real_escape_string($conn, $_POST['design_name']);
                
                if (isset($_FILES['svg_file']) && $_FILES['svg_file']['error'] === 0) {
                    $upload_dir = '../uploads/';
                    if (!file_exists($upload_dir)) {
                        mkdir($upload_dir, 0777, true);
                    }
                    
                    $file_extension = pathinfo($_FILES['svg_file']['name'], PATHINFO_EXTENSION);
                    if (strtolower($file_extension) === 'svg') {
                        $filename = uniqid() . '.svg';
                        $filepath = $upload_dir . $filename;
                        
                        if (move_uploaded_file($_FILES['svg_file']['tmp_name'], $filepath)) {
                            $sql = "INSERT INTO designs (collection_id, name, svg_file, created_at) VALUES ($collection_id, '$design_name', '$filename', NOW())";
                            if (mysqli_query($conn, $sql)) {
                                $success = "Design uploaded successfully!";
                            } else {
                                $error = "Error saving design: " . mysqli_error($conn);
                            }
                        } else {
                            $error = "Error uploading file.";
                        }
                    } else {
                        $error = "Please upload an SVG file.";
                    }
                } else {
                    $error = "Please select a file to upload.";
                }
                break;
        }
    }
}

// Fetch collections
$collections_result = mysqli_query($conn, "SELECT * FROM collections ORDER BY created_at DESC");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ColorCraft Admin Panel</title>
    <link rel="stylesheet" href="css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="logo">
                <i class="fas fa-palette"></i>
                <span>ColorCraft Admin</span>
            </div>
            <nav class="nav-menu">
                <a href="#collections" class="nav-item active" data-section="collections">
                    <i class="fas fa-folder"></i>
                    <span>Collections</span>
                </a>
                <a href="#designs" class="nav-item" data-section="designs">
                    <i class="fas fa-images"></i>
                    <span>Designs</span>
                </a>
                <a href="#analytics" class="nav-item" data-section="analytics">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analytics</span>
                </a>
                <a href="logout.php" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <h1>Admin Dashboard</h1>
                <div class="header-actions">
                    <button class="btn btn-primary" id="newCollectionBtn">
                        <i class="fas fa-plus"></i>
                        New Collection
                    </button>
                </div>
            </header>

            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo $success; ?>
                </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Collections Section -->
            <section id="collections-section" class="content-section active">
                <div class="section-header">
                    <h2>Collections</h2>
                    <p>Manage your coloring book collections</p>
                </div>

                <div class="collections-grid">
                    <?php while ($collection = mysqli_fetch_assoc($collections_result)): ?>
                        <?php
                        $collection_id = $collection['id'];
                        $designs_count = mysqli_fetch_assoc(mysqli_query($conn, "SELECT COUNT(*) as count FROM designs WHERE collection_id = $collection_id"))['count'];
                        ?>
                        <div class="collection-card">
                            <div class="collection-header">
                                <h3><?php echo htmlspecialchars($collection['name']); ?></h3>
                                <div class="collection-actions">
                                    <button class="btn-icon" onclick="generateQR(<?php echo $collection_id; ?>)">
                                        <i class="fas fa-qrcode"></i>
                                    </button>
                                    <button class="btn-icon" onclick="uploadDesign(<?php echo $collection_id; ?>)">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                            <p class="collection-description"><?php echo htmlspecialchars($collection['description']); ?></p>
                            <div class="collection-stats">
                                <span class="stat">
                                    <i class="fas fa-images"></i>
                                    <?php echo $designs_count; ?> designs
                                </span>
                                <span class="stat">
                                    <i class="fas fa-calendar"></i>
                                    <?php echo date('M j, Y', strtotime($collection['created_at'])); ?>
                                </span>
                            </div>
                            <div class="collection-link">
                                <input type="text" readonly value="<?php echo "http://" . $_SERVER['HTTP_HOST'] . "/CB2/collection.php?id=" . $collection_id; ?>" class="collection-url">
                                <button class="btn-copy" onclick="copyToClipboard(this)">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>
            </section>

            <!-- Designs Section -->
            <section id="designs-section" class="content-section">
                <div class="section-header">
                    <h2>All Designs</h2>
                    <p>View and manage all uploaded designs</p>
                </div>

                <div class="designs-grid">
                    <?php
                    $designs_result = mysqli_query($conn, "
                        SELECT d.*, c.name as collection_name 
                        FROM designs d 
                        JOIN collections c ON d.collection_id = c.id 
                        ORDER BY d.created_at DESC
                    ");
                    
                    while ($design = mysqli_fetch_assoc($designs_result)):
                    ?>
                        <div class="design-card">
                            <div class="design-preview">
                                <?php if (file_exists("../uploads/" . $design['svg_file'])): ?>
                                    <img src="../uploads/<?php echo $design['svg_file']; ?>" alt="<?php echo htmlspecialchars($design['name']); ?>">
                                <?php else: ?>
                                    <div class="no-preview">
                                        <i class="fas fa-image"></i>
                                        <span>No preview</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="design-info">
                                <h4><?php echo htmlspecialchars($design['name']); ?></h4>
                                <p class="design-collection"><?php echo htmlspecialchars($design['collection_name']); ?></p>
                                <span class="design-date"><?php echo date('M j, Y', strtotime($design['created_at'])); ?></span>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>
            </section>

            <!-- Analytics Section -->
            <section id="analytics-section" class="content-section">
                <div class="section-header">
                    <h2>Analytics</h2>
                    <p>View usage statistics and insights</p>
                </div>

                <div class="analytics-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-folder"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo mysqli_num_rows(mysqli_query($conn, "SELECT id FROM collections")); ?></h3>
                            <p>Total Collections</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-images"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo mysqli_num_rows(mysqli_query($conn, "SELECT id FROM designs")); ?></h3>
                            <p>Total Designs</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="stat-info">
                            <h3>0</h3>
                            <p>Total Views</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <div class="stat-info">
                            <h3>0</h3>
                            <p>Downloads</p>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Modals -->
    <!-- New Collection Modal -->
    <div class="modal" id="newCollectionModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create New Collection</h3>
                <button class="modal-close" onclick="closeModal('newCollectionModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form method="POST" class="modal-body">
                <input type="hidden" name="action" value="create_collection">
                <div class="form-group">
                    <label for="collection_name">Collection Name</label>
                    <input type="text" id="collection_name" name="collection_name" required>
                </div>
                <div class="form-group">
                    <label for="collection_description">Description</label>
                    <textarea id="collection_description" name="collection_description" rows="3"></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('newCollectionModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Collection</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Upload Design Modal -->
    <div class="modal" id="uploadDesignModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Upload Design</h3>
                <button class="modal-close" onclick="closeModal('uploadDesignModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form method="POST" enctype="multipart/form-data" class="modal-body">
                <input type="hidden" name="action" value="upload_design">
                <input type="hidden" id="upload_collection_id" name="collection_id">
                <div class="form-group">
                    <label for="design_name">Design Name</label>
                    <input type="text" id="design_name" name="design_name" required>
                </div>
                <div class="form-group">
                    <label for="svg_file">SVG File</label>
                    <input type="file" id="svg_file" name="svg_file" accept=".svg" required>
                    <small>Please upload an SVG file</small>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('uploadDesignModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Upload Design</button>
                </div>
            </form>
        </div>
    </div>

    <!-- QR Code Modal -->
    <div class="modal" id="qrModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Collection QR Code</h3>
                <button class="modal-close" onclick="closeModal('qrModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="qr-container">
                    <canvas id="qrCanvas"></canvas>
                </div>
                <div class="qr-actions">
                    <button class="btn btn-primary" onclick="downloadQR()">
                        <i class="fas fa-download"></i>
                        Download QR Code
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/admin.js"></script>
</body>
</html>