class AdminPanel {
    constructor() {
        this.currentQRUrl = '';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupNavigation();
    }

    setupEventListeners() {
        // New Collection Button
        document.getElementById('newCollectionBtn').addEventListener('click', () => {
            this.openModal('newCollectionModal');
        });

        // Navigation
        document.querySelectorAll('.nav-item[data-section]').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.currentTarget.dataset.section;
                this.showSection(section);
                this.updateActiveNav(e.currentTarget);
            });
        });

        // Modal background clicks
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal(modal.id);
                }
            });
        });
    }

    setupNavigation() {
        // Show collections section by default
        this.showSection('collections');
    }

    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });

        // Show target section
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
        }
    }

    updateActiveNav(activeItem) {
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        activeItem.classList.add('active');
    }

    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('active');
            document.body.style.overflow = 'auto';
        }
    }

    generateQR(collectionId) {
        const baseUrl = window.location.origin + '/CB2/collection.php?id=' + collectionId;
        this.currentQRUrl = baseUrl;
        
        const canvas = document.getElementById('qrCanvas');
        
        // Clear previous QR code
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Generate new QR code
        QRCode.toCanvas(canvas, baseUrl, {
            width: 256,
            height: 256,
            color: {
                dark: '#1a1a2e',
                light: '#ffffff'
            },
            margin: 2
        }, (error) => {
            if (error) {
                console.error('QR Code generation failed:', error);
                this.showNotification('Failed to generate QR code', 'error');
            } else {
                this.openModal('qrModal');
            }
        });
    }

    downloadQR() {
        const canvas = document.getElementById('qrCanvas');
        const link = document.createElement('a');
        link.download = `collection-qr-${Date.now()}.png`;
        link.href = canvas.toDataURL();
        link.click();
        
        this.showNotification('QR code downloaded successfully!', 'success');
    }

    uploadDesign(collectionId) {
        document.getElementById('upload_collection_id').value = collectionId;
        this.openModal('uploadDesignModal');
    }

    copyToClipboard(button) {
        const input = button.previousElementSibling;
        input.select();
        input.setSelectionRange(0, 99999); // For mobile devices
        
        try {
            document.execCommand('copy');
            this.showNotification('Link copied to clipboard!', 'success');
            
            // Visual feedback
            const originalIcon = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i>';
            button.style.color = '#4caf50';
            
            setTimeout(() => {
                button.innerHTML = originalIcon;
                button.style.color = '';
            }, 2000);
        } catch (err) {
            this.showNotification('Failed to copy link', 'error');
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        const icon = type === 'success' ? 'check-circle' : 
                    type === 'error' ? 'exclamation-circle' : 'info-circle';
        
        notification.innerHTML = `
            <i class="fas fa-${icon}"></i>
            <span>${message}</span>
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? 'rgba(76, 175, 80, 0.9)' : 
                        type === 'error' ? 'rgba(244, 67, 54, 0.9)' : 
                        'rgba(33, 150, 243, 0.9)'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            font-weight: 500;
            z-index: 10001;
            animation: slideInRight 0.5s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.5s ease';
            setTimeout(() => {
                notification.remove();
            }, 500);
        }, 3000);
    }

    // File upload preview
    setupFilePreview() {
        const fileInput = document.getElementById('svg_file');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file && file.type === 'image/svg+xml') {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        // Could add preview functionality here
                        console.log('SVG file loaded');
                    };
                    reader.readAsText(file);
                }
            });
        }
    }

    // Search functionality
    setupSearch() {
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.placeholder = 'Search collections...';
        searchInput.className = 'search-input';
        
        searchInput.addEventListener('input', (e) => {
            this.filterCollections(e.target.value);
        });
        
        // Add search input to header if needed
    }

    filterCollections(searchTerm) {
        const collections = document.querySelectorAll('.collection-card');
        const term = searchTerm.toLowerCase();
        
        collections.forEach(card => {
            const title = card.querySelector('h3').textContent.toLowerCase();
            const description = card.querySelector('.collection-description').textContent.toLowerCase();
            
            if (title.includes(term) || description.includes(term)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }

    // Statistics update
    updateStatistics() {
        // This would typically fetch real-time data from the server
        const stats = {
            collections: document.querySelectorAll('.collection-card').length,
            designs: document.querySelectorAll('.design-card').length,
            views: Math.floor(Math.random() * 1000), // Placeholder
            downloads: Math.floor(Math.random() * 500) // Placeholder
        };
        
        // Update stat cards if they exist
        const statCards = document.querySelectorAll('.stat-card');
        if (statCards.length >= 4) {
            statCards[0].querySelector('h3').textContent = stats.collections;
            statCards[1].querySelector('h3').textContent = stats.designs;
            statCards[2].querySelector('h3').textContent = stats.views;
            statCards[3].querySelector('h3').textContent = stats.downloads;
        }
    }
}

// Global functions for inline event handlers
function generateQR(collectionId) {
    adminPanel.generateQR(collectionId);
}

function uploadDesign(collectionId) {
    adminPanel.uploadDesign(collectionId);
}

function copyToClipboard(button) {
    adminPanel.copyToClipboard(button);
}

function closeModal(modalId) {
    adminPanel.closeModal(modalId);
}

function downloadQR() {
    adminPanel.downloadQR();
}

// Initialize admin panel
let adminPanel;
document.addEventListener('DOMContentLoaded', () => {
    adminPanel = new AdminPanel();
});

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .search-input {
        padding: 0.75rem 1rem;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        color: #ffffff;
        font-family: inherit;
        width: 250px;
    }
    
    .search-input:focus {
        outline: none;
        border-color: #64ffda;
        box-shadow: 0 0 0 2px rgba(100, 255, 218, 0.2);
    }
    
    .search-input::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }
`;
document.head.appendChild(style);