<?php
include 'admin/config.php';

// Get collection ID from URL
$collection_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($collection_id <= 0) {
    header('HTTP/1.0 404 Not Found');
    exit('Collection not found');
}

// Get collection details
$collection_sql = "SELECT * FROM collections WHERE id = $collection_id";
$collection_result = mysqli_query($conn, $collection_sql);

if (!$collection_result || mysqli_num_rows($collection_result) == 0) {
    header('HTTP/1.0 404 Not Found');
    exit('Collection not found');
}

$collection = mysqli_fetch_assoc($collection_result);

// Get designs in this collection
$designs_sql = "SELECT * FROM designs WHERE collection_id = $collection_id ORDER BY created_at ASC";
$designs_result = mysqli_query($conn, $designs_sql);

// Log the view
log_collection_view($collection_id);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($collection['name']); ?> - ColorCraft</title>
    <link rel="stylesheet" href="css/collection.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="collection-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-palette"></i>
                    <span>ColorCraft</span>
                </div>
                <div class="collection-info">
                    <h1><?php echo htmlspecialchars($collection['name']); ?></h1>
                    <p><?php echo htmlspecialchars($collection['description']); ?></p>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <div class="designs-section">
                <div class="section-header">
                    <h2>Choose a Design to Color</h2>
                    <p>Select any design below to start your coloring journey</p>
                </div>

                <?php if (mysqli_num_rows($designs_result) > 0): ?>
                    <div class="designs-grid">
                        <?php while ($design = mysqli_fetch_assoc($designs_result)): ?>
                            <div class="design-card" onclick="selectDesign(<?php echo $design['id']; ?>, '<?php echo htmlspecialchars($design['name']); ?>')">
                                <div class="design-preview">
                                    <?php if (file_exists("uploads/" . $design['svg_file'])): ?>
                                        <div class="svg-container">
                                            <?php echo file_get_contents("uploads/" . $design['svg_file']); ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="no-preview">
                                            <i class="fas fa-image"></i>
                                            <span>Preview not available</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="design-info">
                                    <h3><?php echo htmlspecialchars($design['name']); ?></h3>
                                    <div class="design-actions">
                                        <button class="btn btn-primary">
                                            <i class="fas fa-paint-brush"></i>
                                            Start Coloring
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-images"></i>
                        <h3>No Designs Available</h3>
                        <p>This collection doesn't have any designs yet. Please check back later!</p>
                    </div>
                <?php endif; ?>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <p>&copy; 2024 ColorCraft. Made with <i class="fas fa-heart"></i> for creative minds.</p>
            </div>
        </footer>
    </div>

    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>Loading your coloring experience...</p>
        </div>
    </div>

    <script>
        function selectDesign(designId, designName) {
            // Show loading screen
            document.getElementById('loadingScreen').style.display = 'flex';
            
            // Redirect to coloring app with design
            setTimeout(() => {
                window.location.href = `coloring.php?design=${designId}&collection=<?php echo $collection_id; ?>`;
            }, 1000);
        }

        // Hide loading screen on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                const loadingScreen = document.getElementById('loadingScreen');
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }, 500);
        });

        // Add hover effects to design cards
        document.addEventListener('DOMContentLoaded', () => {
            const designCards = document.querySelectorAll('.design-card');
            
            designCards.forEach(card => {
                card.addEventListener('mouseenter', () => {
                    card.style.transform = 'translateY(-10px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', () => {
                    card.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>