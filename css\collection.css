/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #ffffff;
    min-height: 100vh;
    overflow-x: hidden;
}

/* Collection Container */
.collection-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 50%, rgba(100, 255, 218, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    gap: 2rem;
    position: relative;
    z-index: 1;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #64ffda;
    flex-shrink: 0;
}

.logo i {
    font-size: 2.5rem;
    background: linear-gradient(45deg, #64ffda, #ff6b6b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.collection-info {
    flex: 1;
}

.collection-info h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #ffffff, #64ffda);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.collection-info p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 3rem 0;
}

.designs-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #ffffff;
}

.section-header p {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.7);
}

/* Designs Grid */
.designs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.design-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.design-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(100, 255, 218, 0.1), rgba(255, 107, 107, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.design-card:hover::before {
    opacity: 1;
}

.design-card:hover {
    border-color: rgba(100, 255, 218, 0.4);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
}

.design-preview {
    height: 200px;
    background: rgba(255, 255, 255, 0.02);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
}

.svg-container {
    width: 80%;
    height: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.svg-container svg {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.3));
}

.svg-container svg path {
    stroke: rgba(255, 255, 255, 0.6);
    stroke-width: 2;
    fill: transparent;
    transition: stroke 0.3s ease;
}

.design-card:hover .svg-container svg path {
    stroke: #64ffda;
}

.no-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: rgba(255, 255, 255, 0.4);
}

.no-preview i {
    font-size: 3rem;
}

.design-info {
    padding: 1.5rem;
    position: relative;
    z-index: 1;
}

.design-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 1rem;
    text-align: center;
}

.design-actions {
    display: flex;
    justify-content: center;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    text-decoration: none;
    font-family: inherit;
}

.btn-primary {
    background: linear-gradient(45deg, #64ffda, #4ecdc4);
    color: #1a1a2e;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(100, 255, 218, 0.4);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: rgba(255, 255, 255, 0.6);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    color: rgba(100, 255, 218, 0.3);
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #ffffff;
}

.empty-state p {
    font-size: 1rem;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    margin-top: auto;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
}

.footer-content p {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
}

.footer-content i {
    color: #ff6b6b;
    margin: 0 0.25rem;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
    color: #ffffff;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(100, 255, 218, 0.2);
    border-top: 4px solid #64ffda;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content p {
    font-size: 1.1rem;
    color: #64ffda;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }
    
    .collection-info h1 {
        font-size: 2rem;
    }
    
    .designs-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .section-header h2 {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .header-content,
    .designs-section {
        padding: 0 1rem;
    }
    
    .designs-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .collection-info h1 {
        font-size: 1.5rem;
    }
    
    .section-header h2 {
        font-size: 1.3rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Floating background elements */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(100, 255, 218, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 107, 107, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}