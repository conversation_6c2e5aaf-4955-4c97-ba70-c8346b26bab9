# ColorCraft - Digital Coloring Book Web Application

A beautiful, innovative digital coloring book web application with admin panel for managing collections and designs.

## Features

### User Features
- **Beautiful Dark UI**: Modern, attractive dark theme with smooth animations
- **Innovative Coloring Tools**: 
  - Brush tool for precise coloring
  - Fill tool for quick area filling
  - Eraser tool for corrections
  - Clear all functionality
- **SVG Path-Based Coloring**: Colors stay within SVG path boundaries
- **Progress Tracking**: Real-time completion statistics
- **Save & Export**: Save colored designs as PNG or PDF
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Touch Support**: Full touch support for mobile devices

### Admin Features
- **Collection Management**: Create and organize coloring book collections
- **Design Upload**: Upload SVG designs to collections
- **QR Code Generation**: Generate QR codes for easy collection sharing
- **Analytics Dashboard**: View usage statistics and insights
- **Secure Admin Panel**: Password-protected admin interface

## Installation

### Prerequisites
- XAMPP (or any PHP server with MySQL)
- Web browser with modern JavaScript support

### Setup Instructions

1. **Install XAMPP**
   - Download and install XAMPP from https://www.apachefriends.org/
   - Start Apache and MySQL services

2. **Clone/Download the Project**
   - Place the CB2 folder in your XAMPP htdocs directory
   - Path should be: `c:\xampp\htdocs\CB2\`

3. **Database Setup**
   - The application will automatically create the database and tables
   - Default database name: `colorcraft_db`
   - Default admin credentials:
     - Username: `admin`
     - Password: `admin123`

4. **Access the Application**
   - Main App: `http://localhost/CB2/`
   - Admin Panel: `http://localhost/CB2/admin/`
   - Collection Example: `http://localhost/CB2/collection.php?id=1`

## Usage

### Admin Panel

1. **Login to Admin Panel**
   - Go to `http://localhost/CB2/admin/`
   - Use default credentials (admin/admin123)

2. **Create a Collection**
   - Click "New Collection" button
   - Enter collection name and description
   - Click "Create Collection"

3. **Upload Designs**
   - Click the "+" button on any collection card
   - Enter design name
   - Upload an SVG file
   - Click "Upload Design"

4. **Generate QR Code**
   - Click the QR code icon on any collection card
   - Download the QR code image
   - Share the QR code or collection link

### User Experience

1. **Access Collection**
   - Scan QR code or visit collection link
   - Browse available designs
   - Click on any design to start coloring

2. **Coloring**
   - Select colors from the palette or use custom color picker
   - Choose tools (Brush, Fill, Eraser)
   - Click on SVG paths to color them
   - Watch progress bar fill as you complete the design

3. **Save Your Work**
   - Click "Save" or "Download" button
   - Choose PNG or PDF format
   - Download your colored masterpiece

## File Structure

```
CB2/
├── admin/                  # Admin panel
│   ├── css/
│   │   └── admin.css      # Admin panel styles
│   ├── js/
│   │   └── admin.js       # Admin panel functionality
│   ├── config.php         # Database configuration
│   ├── index.php          # Admin dashboard
│   ├── login.php          # Admin login page
│   └── logout.php         # Logout functionality
├── css/
│   ├── style.css          # Main app styles
│   └── collection.css     # Collection page styles
├── js/
│   ├── coloringApp.js     # Main coloring application
│   └── coloringEngine.js  # Enhanced coloring engine
├── sample_designs/
│   └── butterfly.svg      # Sample SVG design
���── uploads/               # Uploaded SVG files (auto-created)
├── index.html            # Main coloring interface
├── collection.php        # Collection viewer
├── coloring.php          # Individual design coloring page
└── README.md             # This file
```

## Technical Features

### SVG Path-Based Coloring
- Colors are applied directly to SVG paths
- Maintains vector quality at any zoom level
- Precise boundary detection
- Smooth color transitions

### Responsive Design
- Mobile-first approach
- Touch-friendly interface
- Adaptive layouts for all screen sizes
- Optimized performance

### Database Schema
- `collections`: Store collection information
- `designs`: Store design metadata and file references
- `admin_users`: Admin authentication
- `collection_views`: Track collection views
- `design_downloads`: Track download statistics

## Browser Compatibility

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+
- Mobile browsers with modern JavaScript support

## Security Features

- SQL injection prevention
- XSS protection
- Secure file upload validation
- Session-based authentication
- CSRF protection

## Customization

### Adding New Color Palettes
Edit the preset colors in `css/style.css` and update the color swatches in the HTML files.

### Modifying UI Theme
Update the CSS custom properties in the main stylesheet to change colors, fonts, and spacing.

### Adding New Tools
Extend the `ColoringEngine` class in `js/coloringEngine.js` to add new coloring tools.

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Ensure MySQL is running in XAMPP
   - Check database credentials in `admin/config.php`

2. **File Upload Issues**
   - Ensure `uploads/` directory has write permissions
   - Check PHP file upload settings

3. **SVG Not Displaying**
   - Verify SVG file is valid
   - Check file path and permissions

### Performance Optimization

- Use compressed SVG files
- Optimize images and assets
- Enable browser caching
- Consider CDN for static assets

## License

This project is open source and available under the MIT License.

## Support

For support and questions, please check the documentation or create an issue in the project repository.

## Contributing

Contributions are welcome! Please read the contributing guidelines before submitting pull requests.

---

**ColorCraft** - Making digital coloring beautiful and accessible for everyone! 🎨