<?php
// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'colorcraft_db';

// Create connection
$conn = mysqli_connect($host, $username, $password);

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Create database if it doesn't exist
$sql = "CREATE DATABASE IF NOT EXISTS $database";
if (!mysqli_query($conn, $sql)) {
    die("Error creating database: " . mysqli_error($conn));
}

// Select the database
mysqli_select_db($conn, $database);

// Create tables if they don't exist
$tables = [
    "CREATE TABLE IF NOT EXISTS collections (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )",
    
    "CREATE TABLE IF NOT EXISTS designs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        collection_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        svg_file VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (collection_id) REFERENCES collections(id) ON DELETE CASCADE
    )",
    
    "CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )",
    
    "CREATE TABLE IF NOT EXISTS collection_views (
        id INT AUTO_INCREMENT PRIMARY KEY,
        collection_id INT NOT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (collection_id) REFERENCES collections(id) ON DELETE CASCADE
    )",
    
    "CREATE TABLE IF NOT EXISTS design_downloads (
        id INT AUTO_INCREMENT PRIMARY KEY,
        design_id INT NOT NULL,
        ip_address VARCHAR(45),
        download_type ENUM('png', 'pdf') NOT NULL,
        downloaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (design_id) REFERENCES designs(id) ON DELETE CASCADE
    )"
];

foreach ($tables as $table_sql) {
    if (!mysqli_query($conn, $table_sql)) {
        die("Error creating table: " . mysqli_error($conn));
    }
}

// Create default admin user if it doesn't exist
$admin_check = mysqli_query($conn, "SELECT id FROM admin_users WHERE username = 'admin'");
if (mysqli_num_rows($admin_check) == 0) {
    $default_password = password_hash('admin123', PASSWORD_DEFAULT);
    $sql = "INSERT INTO admin_users (username, password) VALUES ('admin', '$default_password')";
    mysqli_query($conn, $sql);
}

// Set timezone
date_default_timezone_set('UTC');

// Helper functions
function sanitize_input($data) {
    global $conn;
    return mysqli_real_escape_string($conn, trim($data));
}

function generate_unique_filename($extension) {
    return uniqid() . '_' . time() . '.' . $extension;
}

function log_collection_view($collection_id) {
    global $conn;
    $ip = $_SERVER['REMOTE_ADDR'];
    $user_agent = $_SERVER['HTTP_USER_AGENT'];
    
    $sql = "INSERT INTO collection_views (collection_id, ip_address, user_agent) VALUES ($collection_id, '$ip', '$user_agent')";
    mysqli_query($conn, $sql);
}

function log_design_download($design_id, $type) {
    global $conn;
    $ip = $_SERVER['REMOTE_ADDR'];
    
    $sql = "INSERT INTO design_downloads (design_id, ip_address, download_type) VALUES ($design_id, '$ip', '$type')";
    mysqli_query($conn, $sql);
}

function get_collection_stats($collection_id) {
    global $conn;
    
    $stats = [];
    
    // Get design count
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM designs WHERE collection_id = $collection_id");
    $stats['designs'] = mysqli_fetch_assoc($result)['count'];
    
    // Get view count
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM collection_views WHERE collection_id = $collection_id");
    $stats['views'] = mysqli_fetch_assoc($result)['count'];
    
    // Get download count
    $result = mysqli_query($conn, "
        SELECT COUNT(*) as count 
        FROM design_downloads dd 
        JOIN designs d ON dd.design_id = d.id 
        WHERE d.collection_id = $collection_id
    ");
    $stats['downloads'] = mysqli_fetch_assoc($result)['count'];
    
    return $stats;
}

function get_total_stats() {
    global $conn;
    
    $stats = [];
    
    // Total collections
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM collections");
    $stats['collections'] = mysqli_fetch_assoc($result)['count'];
    
    // Total designs
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM designs");
    $stats['designs'] = mysqli_fetch_assoc($result)['count'];
    
    // Total views
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM collection_views");
    $stats['views'] = mysqli_fetch_assoc($result)['count'];
    
    // Total downloads
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM design_downloads");
    $stats['downloads'] = mysqli_fetch_assoc($result)['count'];
    
    return $stats;
}
?>