<?xml version="1.0" encoding="UTF-8"?>
<svg version="1.1" viewBox="0 0 2048 1536" width="1024" height="768" xmlns="http://www.w3.org/2000/svg">
<path transform="translate(0)" d="m0 0h2048v1536h-2048z" fill="#FEFFFE"/>
<path transform="translate(1433,75)" d="m0 0h28l15 4 10 6 7 8 10 21 8 22 6 25 3 20 1 11v32l-2 20-4 22-7 25-7 20-11 24-12 23-9 15-20 32-6 12-1 4-1 13-1 45-3 12-7 12-11 12-10 7-14 7-25 9-8 7-15 23-10 18-10 13-6 8 11 8 11 9 10 10 10 14 9 13 13 18 10 14 13 17 18 27 12 22 8 20 6 20 5 26 1 8v48l-4 28-5 23-5 17 1 4 15 15 11 14 10 15 11 21 5 15 4 20 1 9v23l-3 20-5 19-6 16-10 19-10 15-9 11-8 11-2 8v15l4 10 10 13 7 10 6 12 3 11v13l-4 7-6 7-10 8-8 5-3 1h-9l-8-1-11 6-9 2h-9l-24-4-7 1-3 1h-7l-13-4 13 18 10 16 6 15 3 11 1 7v13l-3 8-11 13-7 7-5 2h-13l-12 7-8 2h-39l-12 2h-17l-21-4-11-4-3-2h-6l-6 2-17-8-18-11-7-8-4-13-1-8v-28l4-26v-41l3-16 8-23 6-16-19 5-17 3-22 2h-38l-18-2-12-2h-6l-7 8-9 9-7 8-11 13-7 8-7 6-21 7-11 5-20 3-12 2h-21l-15-3-18-2-13-5-12-9-4-5-1-3v-14l4-17 7-16 7-11 11-12 11-10 6-8-2-5-8-7-16-11-11-9-14-12-15-14-16-15-3-2-3 12-6 22-7 35-2 15-1 14v22l3 40 2 30v9l-2 13-6 17-14 28-11 20-7 8-7 3-23 5-15 5-4 1h-14l-14-5-10 1-17 2h-26l-11-4-11-7-9 1-8-2-10-7-8-7-6-8-3-7v-17l4-15 9-17 11-14 10-10 7-8 10-13 9-16 5-17 3-18v-9l-3-19-12-48-8-28-11-42-3-14-2-17v-20l2-19 5-22 9-26 5-12-1-14-4-28-1-14v-27l2-21 1-12-8-15-4-13v-123l2-19 8-22 16-32-1-12-1-6v-16l4-27 7-28 5-17-1-10-5-27v-14l4-8-1-5-4-9v-17l4-21 1-8v-21l-8-28-15-39-8-24-7-28-3-19-1-12v-22l2-18 5-21 8-21 10-19 9-12 9-9 10-6 13-5 13-2 16 1 14 4 17 9 12 9 12 11 11 11 9 11 15 20 12 21 15 28 11 14 13 16 10 13 8 9 15-3 21-5 13-2 15 1 31 6 29 5 12 1 29-1 31-1 22 3 42 9 32 8h4l2-4 9-8 9-4 16-5 9-6 22-22 8-7 17-17 1-2h2v-2h2l2-4 13-13 7-8 22-22 8-7 10-9 17-13 17-12 15-10 14-8 25-11 19-6 14-3z" fill="#050505"/>
<path transform="translate(1313,768)" d="m0 0 2 2-7 34-7 21-8 18-8 16-12 23-14 22-12 19-11 16-13 19-1 4v2l4 2 8 7 14 10 9 6-5 5-27 14-32 13-6 2 12-1 36-7 25-7 16-7h6l15 6 26 8 24 5 16 2h24l9-6 20-20h2v42l-2 18-5 21-8 23-7 14-10 13-11 13-7 8-11 12-9 10-11 13-12 13-11 14-10 13-4 11-1 7v29l4 16 7 11 11 12 19 19 13 17 6 10 7 15 4 16v12h-2l-3-6-3-2-9 1-5 4-2 4h-2l-5-19-6-20-8-16-7-6-8-2 2 6 7 9 7 15 4 15 5 27v6l-8-2h-9l-11 4-4 2-6-1-6-4-4-5-3-15-4-27-4-15-5-10-8-7-7-2 9 14 5 17 3 19 3 33-6-1-2-1h-14l-8 3-5 3-7-1-6-4-4-6-3-10-1-7-2-35-4-10-5-4-9-2 4 7 5 8 1 4v45l-7-6-3-2-8-1-9 3-1 2-4-1-4-5-3-12-1-12v-10l2-14 7-25 1-5-1-16v-22l5-21 10-25 16-33 10-21 12-22 9-17 10-23 11-29 15-45 9-15 4-8-9 6-10 9-13 10-16 8-20 8-19 6-34 7-38 6-69 8-50 4-17 1h-19l-25-5-29-8-89-26-27-8-19-7-6-2 10 7 16 9 15 7 17 6 67 17 46 10 27 4h78l30-2 28-4 57-11 22-4h6l-3 11-11 35-3 3-17 10-29 12-20 6-18 4h13l15-1h54l5 1 6 30 2 7-14 6-28 9-26 6-25 4-19 2h-39l-24-3-25-6-23-8-21-10-15-10-19-12-14-9-16-11-15-12-11-9-13-12-7-7-5-4-7-8-17-17-24-16-16-13-12-11-9-10-3-5 2-7 11-15 1-1h8l23 6 20 7 1-1-18-18-11-14-13-17-22-33-2-5h-2l8 20 5 15v10l-4 5-10 7-15 7-35 13-6 1 5 5 10 9 8 9 8 13 5 12 7 25 4 6 10 7 23 13 11 9 7 8 4 11v25l-5 52v95l-4 17-11 35-7 13-14 15-9 6-6 2-11-4-6-1h-11l-10 3-3 1 2-16 4-19 5-13 6-11-1-8-1-4-6-2v7l-4 9-6 12-8 25-5 12-6 8-9 6h-7l-11-6-8-4-4-1h-12l-6 1 2-13 6-22 5-13 5-9 12-14 3-4-5-1-8 4-7 6-6 10-7 15-9 27-1 4h-2l-6-9-6-3-9 1-4 3-3-1-1-6 3-16 8-16 7-9 7-8 8-7 12-13 9-13 6-13 7-23 5-19 1-13-8-32-13-42-13-41-6-24-2-13-1-14v-12l2-20 5-21 6-16 5-11 2-4 3 3 5 10-1-10-8-39-3-23v-37l2-15 3-1 14 6 4 1h21l12-1h17l13 5 19 9 17 5 48 10 20 6 12 5 12 8 10 8 11 9 23 2 29 7 18 6 27 12 25 13 5 3-15-29-6-15v-2h18l21 5 31 9 17 3h17l17-4 24-9 15-6 35-11 23-10 18-10 14-10 19-14 16-13 13-11 15-14 10-11 12-17 10-17z" fill="#FEFFFE"/>
<path transform="translate(821,441)" d="m0 0h40l24 3 18 5 16 8 14 8 18 12 13 10 13 11 10 9 9 8 7 8 11 14 13 21 10 19 18 50 8 22 12 26 19 40 16 32 9 16 8 16 8 19 5 19 2 13v20l-4 15-8 16-8 11-8 8-14 8-12 5-17 4-15-1-23-6-19-8-23-12-20-13-10-8-12-11-8-9-18-27-16-27-9-15-14-24-2-4-4-25-6-16-9-17-10-14-12-14-18-18-8-13-4-13v-18l-2-1 34-15 23-15 14-10 15-12 12-7 10-2 11 3 16 9 17 12 11 10 8 7 8 8 8 7 16 16 9 11 13 18 13 23 10 23 7 24 7 29 7 31 8 24 9 20 9 13 7 7 8 4 5 1 1-9-4-13-10-23-13-29-22-55-12-26-12-23-10-18-9-16-11-15-14-15-9-9-11-9-34-34-8-7-16-16-5-10-7-19-7-11-9-7-16-8-16-6-25-5-12-1h-11l-23 2-15 4-14 7-11 8-9 8-11 14-7 9-8 7-18 10-20 13-10 9-8 7-1-2 10-19 8-11 8-8 7-8 29-29 8-7 11-8 19-11 17-6 20-4z" fill="#FEFFFE"/>
<path transform="translate(1437,144)" d="m0 0 4 1 5 6 5 13 4 19 2 23v29l-2 25-4 23-6 24-10 30-11 24-14 25-8 12-8 10 6-4 13-13 5-4 7-8 8-8 7-8 12-13 9-11 8-9 8-10 4-4-3 10-14 29-13 22-13 19-9 11-12 13-14 14-8 7-10 9-7 5-1 2 23-4 9-3 5-5 11-13h1v45l-3 10-8 11-11 11-12 7-24 8h-11l-16-3-2-7-6-39-6-28-18-64-6-22-3-20v-22l5-21 10-26 8-17 12-21 11-16 13-16 28-28 17-13 14-9 12-6z" fill="#FEFFFE"/>
<path transform="translate(897,259)" d="m0 0h11l19 5 27 9v3l-8 7-10 10-10 13-5 7 14-11 16-12 13-8 8-4 15-5 14-2h29l13 4 12 7 14 11 13 12 16 14-2 1-14-8-15-10-20-13-16-8 2 4 6 10 7 18 5 13 6 9 7 7 8 7 9 9 1 6-12-3-18-7-12-2h-12l-21 3-18 6-11 7-7 6-9 11-7 10-9 16-6 16-1 5v17l1 10-5-2-21-11-8-8-4-9-2-14 1-20 3-19 5-18 2-7-7 10-18 40-6 12h-2v-36l-2-26-1-9h-2v17l-3 15-5 14-9 15-12 13-9 8-14 7-15 4-15 3h-6l4-16 1-18-2-37-2-16-3-8-6-4-8-1-13 4-17 8-6 1 4-6 9-10 30-30 11-9 11-10 10-7 13-4 47-10 22-5z" fill="#FEFFFE"/>
<path transform="translate(671,93)" d="m0 0h15l13 3 13 7 12 9 10 9 7 7 9 11 13 17 12 20 10 19 9 22 1 4v19l-4 23-5 16-6 10-7 8-7 5h-2l2-10 6-29 2-16v-13l-5-17-8-20-8-16-8-14-9-12-5-6-12-9-16-8-26-7 9 11 12 15 12 19 13 21 10 18 8 16 9 20 5 17 2 12v19l-2 13-3 5-10 8-9 7-9 9-11 19-8 13-8 7-19 14-5 4-8-15-14-24-11-21-12-28-7-19-8-28-5-25-2-17v-20l2-17 5-21 8-19 8-15 9-12 10-10 10-5z" fill="#FEFFFE"/>
<path transform="translate(744,696)" d="m0 0h26l18 2 16 4 11 6 12 11 7 8 8 13 8 16 6 16 4 19 8 5 18 10 13 8 13 21 9 15 8 12-1 4-14 7-16 4h-12l-18-4-20-9-15-9-14-11-15-15-11-15-9-16-7-17-6-19-3-16-2-23v-7h-2l-1 10v23l3 19 6 22 8 19 8 15 11 16-1 3-22 11-9 3h-13l-14-7-10-9-9-10-12-18-9-15-5-19-2-12v-23l4-16 7-14 8-9 10-5 12-3z" fill="#FEFFFE"/>
<path transform="translate(1060,413)" d="m0 0h15l18 4 12 5 14 8 12 9 10 9 10 11 11 15 12 22 5 11v7l-9 21-10 17-10 14-7 8-14 9-21 11-21 7h-9l-13-4 4 10 8 24 13 33 12 25 9 16 15 26 4 7-6-33-2-16-1-26v-14l1-15 6-8 7-7 8-7 9-9 9-11 10-11 11-14 10-14 3-4 2 1-10 22-12 22-10 18-12 21-7 15-4 14-2 16v43l3 13 10 19 9 16 4 11 1 5v33l-4 20-1 6 10-14 12-14 12-10 17-12 30-23 16-13 13-12 10-11 11-15 10-18 8-21 4-20 2-16h1l1 14v51l-4 24-5 17-8 16-6 11-12 17-9 11-9 10-16 16-14 11-24 16-17 9-22 10-30 10 1-5 7-16 3-11 3-24-1-25-3-16-7-19-8-15-12-19-13-21-11-18-9-16-12-26-12-32-10-30-9-21-10-19 1-2 14 8 12 4 18 2 20-1 15-4 15-8 12-10 11-14 11-16v-2l-9-7-8-8-13-17-12-13-8-7-14-10-12-7-14-6-4-1-4-4-11-8-5-7-2-2z" fill="#FEFFFE"/>
<path transform="translate(729,526)" d="m0 0 2 1 9 29 7 14 10 13 10 12-7 13-8 10-11 10-15 10-12 9-9 8-12 16-7 12-6 17-8 17-6 21-10 59-4 29-4 11-4 5-2 1h-7l-8-5-7-8-7-15-3-12v-111l3-20 7-21 14-29 11-20 16-23 11-14 11-12 10-9 18-11 12-5z" fill="#FEFFFE"/>
<path transform="translate(1432,87)" d="m0 0h27l13 4 3 3-29 2-20 4-17 5-17 7-16 10-11 8-11 9-15 14-7 8-13 15-14 19-14 20-11 17-3 7-1 5-1 17-1 32v72l2 29-3-3-10-18-8-16-5-15-3-15-1-9-1-21-1-62-13 14-8 7-9 8-14 9-26 12h-2l7 8 10 11 10 13 13 20 12 18 15 22 17 25 13 18 7 14 3 14 6 58 3 20 1 4h-2l-11-18-10-16-4-8-8-49-6-16-8-12-14-18-10-13-11-14-14-21-9-16-13-33-4-19 10-4 9-4 9-6 10-9 11-9 8-8 8-7 9-9 7-8 10-10 7-8 11-11 7-8 8-7 15-14 10-8 13-11 14-10 21-13 27-14 22-8 13-3z" fill="#FEFFFE"/>
<path transform="translate(1443,972)" d="m0 0 5 3 7 7 11 15 8 14 7 15 5 17 2 12v34l-3 17-5 17-8 18-8 14-12 16-11 12-17 12-19 9-15 4-9 1h-25l5-10 11-19 12-23 13-24 11-20 11-25 10-30 6-27 4-26 3-30z" fill="#FEFFFE"/>
<path transform="translate(760,664)" d="m0 0h21l12 3 13 7 14 11 8 7 11 15 20 35 15 27 22 38 3 7-5-2-22-13-11-6-2-2-5-22-6-15-8-16-10-15-11-11-11-7-11-4-15-3-12-1h-26l-16 2-13 5-8 7-7 12-5 16-1 5v23l4 21 2 9-3-1-7-9-3-2 2 6 11 22 13 19 4 6-1 2-13-4-11-7-8-9-6-13-4-15-1-11v-18l3-21 2-9h2l1-9h2l1-6 8-12 9-10 11-9 13-9 14-8 11-4z" fill="#545454"/>
<path transform="translate(1405,1218)" d="m0 0 13 1 8 5 7 8 7 9 14 9 9 14 4 10 1 4v8l-6 3-2-5-2-1h-8l-10 5-1 2h-2l-4-16-6-17-9-16-10-6-9-1 5 5 8 9 7 14 7 21 3 12v3l-6-1-12-3-9 1-11 7-5 2-5-5-5-8-6-20-4-21-3-5-3-2h-6l-1 3 5 6 2 9 3 22 1 8h-13l-8 2-5 1-2-3-2-12v-19l4-11 7-11 9-6 14-6 30-7z" fill="#FEFFFE"/>
<path transform="translate(921,1208)" d="m0 0h14l13 4 18 7 16 6 17 7 12 3-6 8-7 8-7 14-6 15-3 3-11-4-12 2 2-14 3-10 4-6 6-5 1-4-3-1-9 4-5 6-4 8-7 26-2 3-5-1-9-4-4-1h-7l-11 4h-2l4-16 5-13 6-9 8-8 6-5-1-3h-6l-8 5-8 8-7 11-7 14-4 12-12-6-9-1-9 1 2-11 7-13 8-10 10-7 7-4v-1h-10l-9 5-3 3-2-1 11-13 10-7 13-6z" fill="#FEFFFE"/>
<path transform="translate(1245,542)" d="m0 0 9 1 8 5 8 9 7 14 5 19 2 19v10l-2 21-5 22-7 19-8 16-7 10-7 8-4 2-5-1-3-5-3-13v-10l5-39 2-22 1-20v-30l-2-32 2-2z" fill="#FEFFFE"/>
<path transform="translate(1350,779)" d="m0 0h15l36 2 32 4 3 9 4 15-1 4-26 36-10 15-9 12-10 14-5 6h-1l-3-30-5-23-9-31-11-30z" fill="#FEFFFE"/>
<path transform="translate(844,473)" d="m0 0h12l15 6 10 6 13 13 6 10 2 7v8l-3 9-5 7-6 3-9 1-9-3-6-5v-6l10-8 3-6v-6l-5-6-6-3h-10l-8 4-8 9-5 10-2 9 1 10 3 5 8 7 15 5 8 2-5 5-14 10-23 11-16 4-9 1 1-6 6-15 5-23 5-25 5-13 13-25 7-11z" fill="#FEFFFE"/>
<path transform="translate(802,469)" d="m0 0h9l12 2 3 3-6 18-7 29-19 38-6 17-3 13-2 1-14-7-12-12-7-14-2-9v-5l12 4 8 1 10-3 10-9 8-16 3-11-1-9-3-5-5-3h-9l-6 3-5 5-1 3v8l3 4v5l-4 5-6 2-8-1-5-4-1-2 1-10 6-12 11-12 8-7 14-7 6-2z" fill="#FEFFFE"/>
<path transform="translate(1338,787)" d="m0 0 2 1 5 33 3 29 1 16v26l-1 14-20-9-17-9-16-10 1-4 14-24 8-15 13-29z" fill="#FEFFFE"/>
<path transform="translate(808,602)" d="m0 0 4 1-1 1 1 18 4 13 9 14 21 21 9 11 10 15 9 17 6 18 2 13v7l-3-3-9-15-14-20-16-21-12-13-15-15-7-8-8-11-4-11-1-5v-18l3-6z" fill="#545454"/>
<path transform="translate(1016,379)" d="m0 0h9l8 3 10 8 11 10 1 4-16 10-14 11-14 12-11 9-6 1-5-6-3-7-1-5v-11l4-10 7-13 8-10 8-5z" fill="#FEFFFE"/>
<path transform="translate(767,595)" d="m0 0 5 2 12 7-3 11-5 12-7 11-7 6-16 8-14 6-18 11-11 9-10 9-13 13 3-12 7-14 12-16 7-8 14-11 15-10 11-9 8-9 9-15z" fill="#535353"/>
<path transform="translate(1354,699)" d="m0 0 9 3 14 7 14 9 10 8 8 8 6 9-10 4-57 18-1-3 4-31 2-26z" fill="#FEFFFE"/>
<path transform="translate(716,371)" d="m0 0h14l7 6 1 2v9l-7 10-10 4-15 4-16 9-11 9-8 9-12 18-7 9-6 4v-7l6-18 8-16 7-11 11-14 7-7 13-10z" fill="#FEFFFE"/>
<path transform="translate(1361,934)" d="m0 0h1l4 37 1 34-12-1-27-5-23-7v-2l8-6 13-12 5-5 12-11 9-11z" fill="#FEFFFE"/>
<path transform="translate(644,133)" d="m0 0h2l-2 10-3 14-1 10v45l4 30 6 24 5 15 9 21 2 3-1 3-8-10-12-19-8-15-9-21-6-22-1-6v-21l3-18 5-15 8-18 5-9z" fill="#0A0A0A"/>
<path transform="translate(1443,852)" d="m0 0h2v23l-3 25-5 23-1 2-28-5-26-5 5-5 14-12 13-13 7-8 10-10 9-11z" fill="#FEFFFE"/>
<path transform="translate(1015,365)" d="m0 0h14l17 3 18 8 19 10 30 20 16 13 16 12 12 10 14 11 13 11 8 11 4 15 3 13v5l-3-4-8-16-7-11-13-14-7-8-10-10-11-9-9-8-16-12-21-14-24-14-19-9-15-5h-23l-9 4-5 5-8 16-5 13h-2l-3-10v-14l5-8 6-7 6-4 8-2z" fill="#FDFEFD"/>
<path transform="translate(1031,477)" d="m0 0h1v20l3 12 5 10 9 10 10 6 7 2 15 1 10-3 11-7 6-5 7-10 4-8 2-6 1-7 5 2 14 12 3 3-8 10-9 8-13 8-14 5-15 3h-14l-16-4-16-8-13-11-21-21v-2l10-8 11-9z" fill="#FEFFFE"/>
<path transform="translate(1052,415)" d="m0 0 4 2 6 8 13 9v2l4 1h-21l-12 3-8 4-10 8-3 4 14-9 14-4h18l12 3 17 9 7 5 4 4 10 8 2 3-4-2-15-10-16-7-7-3-4-1h-23l-9 2-7 4h-3v2l-11 7-10 9-11 9-11 7-4-1 1-3 6-5 4-7 5-5 10-13 5-7 5-5 6-8 3-5 10-11z" fill="#626262"/>
<path transform="translate(1285,891)" d="m0 0 4 2 16 12 13 8 17 8 10 3v2l-9 3-10 2-11 1h-45l-11-1 2-4 18-27z" fill="#FEFFFE"/>
<path transform="translate(1371,928)" d="m0 0 5 1 14 7 19 11 11 6-1 4-11 18-10 14-9 11-2-3-8-37-8-30z" fill="#FEFFFE"/>
<path transform="translate(1229,1096)" d="m0 0h1l-1 12-6 28-9 31-6 13-2 2-4-8-4-16-1-7v-23l2-12 5-5 11-7z" fill="#FEFFFE"/>
<path transform="translate(647,467)" d="m0 0 2 3 3 17 5 15 5 9 6 7 11 7-6 9-10 17-11 20-3 4-2-1-2-28 1-18 2-18v-24z" fill="#FEFFFE"/>
<path transform="translate(1333,945)" d="m0 0 2 1-13 10-19 13-19 12-4 1-19-11-11-8-1-3 25-3 42-8z" fill="#FEFFFE"/>
<path transform="translate(1153,515)" d="m0 0h6l-2 5-8 10-9 11-13 10-11 5-13 3h-26l-18-4-16-8-8-6-3-3v-2l5 2 16 9 10 2 18 1 15-2 20-6 16-8 11-9 8-9z" fill="#FEFFFE"/>
<path transform="translate(738,381)" d="m0 0h1l1 42 3 24 2 9-4-2-7-11-1-4h-2l-3-6-6-5-3-3-12-3-13 2-10 6-4 4-2-1 7-7 10-6 2-1h24l6 5 4 5 3 7v4h2l-5-16-5-8-6-4-7-2h-14l3-3 10-4 15-4 5-3 5-8z" fill="#545454"/>
<path transform="translate(674,779)" d="m0 0 1 2 8 1 3 3 14 28 13 19 4 6-1 2-13-4-11-7-8-9-6-13-4-15z" fill="#FEFFFE"/>
<path transform="translate(936,691)" d="m0 0 5 5 13 18 10 13 11 14 9 11 8 7 14 11 11 8-1 4-8-1-11-7-13-10-10-10-10-14-12-18-12-21-4-8z" fill="#101010"/>
<path transform="translate(1098,280)" d="m0 0 16 4 12 5 12 9 19 19 1 3-5-1-19-8-15-10-12-11-9-9z" fill="#FEFFFE"/>
<path transform="translate(652,378)" d="m0 0 4 4 7 9 6 5-2 5-18 27-1 2h-2l-2-5 1-13 5-25z" fill="#FEFFFE"/>
<path transform="translate(652,468)" d="m0 0 3 1 8 16 8 11 8 6 9 3h8l-5 5-7 6-9-2-9-6-7-11-5-15-2-10z" fill="#FEFFFE"/>
<path transform="translate(771,347)" d="m0 0 5 1 3 3v9l-6 15-4 7-3-3-5-15-4-6-2-1 1-4 11-5z" fill="#FEFFFE"/>
<path transform="translate(846,593)" d="m0 0 8 1 9 5 10 9 8 11 5 8-1 3-15-13-15-11-15-8-1-2z" fill="#0E0E0E"/>
<path transform="translate(738,575)" d="m0 0 8 1 2 3v5l-1 1-17 3-16 6-16 9-2 1 2-5 10-9 14-9 8-4z" fill="#0F0F0F"/>
<path transform="translate(672,631)" d="m0 0h2l-2 4-5 4-11 14-12 17-10 13-6 5-4-1 2-6 8-11 8-9 9-11 10-11z" fill="#131313"/>
<path transform="translate(1062,466)" d="m0 0h9l4 5v7l-5 5h-9l-3-3-1-6 3-6z" fill="#FEFFFE"/>
<path transform="translate(700,434)" d="m0 0 8 2 2 3-1 5-4 6-4 2h-6l-3-4v-6l5-6z" fill="#FEFFFE"/>
<path transform="translate(676,464)" d="m0 0 2 3 2 9 6 10 10 6 7 2-1 2h-12l-8-4-5-6-3-12z" fill="#FEFFFE"/>
<path transform="translate(711,548)" d="m0 0h3l-1 4-12 11-6 4h-4l1-5 10-10 5-3z" fill="#131313"/>
<path transform="translate(1026,714)" d="m0 0 6 2 6 7 4 7v5l-5-1-9-11-2-4z" fill="#131313"/>
<path transform="translate(697,419)" d="m0 0h24l6 5 1 3h-3l-4-1-8-3-9-1-10 2-10 6-4 4-2-1 7-7 10-6z" fill="#FAFBFA"/>
<path transform="translate(903,574)" d="m0 0h5l6 5 6 10v5l-6-3-7-8-4-6z" fill="#131313"/>
<path transform="translate(999,648)" d="m0 0 6 2 7 8 3 5-1 5-4-2-7-7-5-8z" fill="#131313"/>
<path transform="translate(977,670)" d="m0 0 4 1 7 6 5 8 1 4-4 2-9-11-4-7z" fill="#161616"/>
<path transform="translate(957,606)" d="m0 0 6 1 7 7 4 7-1 3-5-1-10-12z" fill="#151515"/>
<path transform="translate(919,622)" d="m0 0 4 1 8 8 4 7-1 4-4-2-7-8-5-8z" fill="#161616"/>
<path transform="translate(879,648)" d="m0 0 5 2 6 8 2 4v5l-5-2-9-12z" fill="#131313"/>
<path transform="translate(673,594)" d="m0 0h5l-1 4-11 10-6 2 1-5 7-8z" fill="#161616"/>
<path transform="translate(711,1396)" d="m0 0 9 1 3 3-9 4-11 2 1-4 5-5z" fill="#FEFFFE"/>
<path transform="translate(1256,1420)" d="m0 0h9l6 4 1 3h-12l-6-2-1-2z" fill="#FEFFFE"/>
<path transform="translate(1354,1297)" d="m0 0 6 2 3 3 1 6-5-1-10-6 2-3z" fill="#FEFFFE"/>
<path transform="translate(1408,1302)" d="m0 0h8l4 2-1 3-11 3-4-1 2-6z" fill="#FEFFFE"/>
<path transform="translate(627,1394)" d="m0 0 8 2 4 5v1h-8l-8-3v-4z" fill="#FEFFFE"/>
<path transform="translate(925,1287)" d="m0 0 5 2 1 4-4 2h-11l2-5z" fill="#FEFFFE"/>
<path transform="translate(875,1281)" d="m0 0 5 1 4 5v4l-6-1-6-4v-4z" fill="#FEFFFE"/>
<path transform="translate(724,607)" d="m0 0h4l-2 4-8 6-5 1v-4l9-6z" fill="#191919"/>
<path transform="translate(1191,1402)" d="m0 0 7 1 4 6 1 7-4-2-8-10z" fill="#FEFFFE"/>
<path transform="translate(706,415)" d="m0 0 10 1 5 2-2 2h-24l1-2z" fill="#535353"/>
<path transform="translate(586,1373)" d="m0 0h3l3 5 1 11-5-5-3-6z" fill="#FEFFFE"/>
<path transform="translate(724,426)" d="m0 0 6 1 6 15-1 2-2-5h-2l-3-6-6-5z" fill="#A3A3A3"/>
<path transform="translate(1322,1418)" d="m0 0h6v4l-9 3h-5l2-4z" fill="#FEFFFE"/>
<path transform="translate(1453,1289)" d="m0 0h3l-1 5-6 8-2 1 1-8 3-5z" fill="#FEFFFE"/>
<path transform="translate(1359,1397)" d="m0 0h2l1 5-5 10-2-2 2-11z" fill="#FEFFFE"/>
<path transform="translate(1103,460)" d="m0 0 5 2 11 8 4 5-4-2-15-10-2-2z" fill="#FEFFFE"/>
<path transform="translate(995,483)" d="m0 0v3l4 1-7 5-4-1 1-3z" fill="#FEFFFE"/>
<path transform="translate(741,443)" d="m0 0 2 4 2 9-4-2-2-3 1-3z" fill="#FEFFFE"/>
<path transform="translate(724,426)" d="m0 0 6 1 1 5h-5l-4-5z" fill="#6E6E6E"/>
<path transform="translate(967,1285)" d="m0 0 6 2-4 3-5 1 2-5z" fill="#FEFFFE"/>
</svg>
