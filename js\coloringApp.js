class ColoringApp {
    constructor() {
        this.currentColor = '#ff6b6b';
        this.currentTool = 'brush';
        this.coloredPaths = new Set();
        this.totalPaths = 0;
        this.designs = [];
        this.currentDesign = null;
        
        this.init();
    }

    init() {
        this.loadDesigns();
        this.setupEventListeners();
        this.hideLoadingScreen();
    }

    setupEventListeners() {
        // Color picker
        const colorPicker = document.getElementById('colorPicker');
        colorPicker.addEventListener('change', (e) => {
            this.currentColor = e.target.value;
            this.updateActiveColorSwatch();
        });

        // Color swatches
        document.querySelectorAll('.color-swatch').forEach(swatch => {
            swatch.addEventListener('click', (e) => {
                this.currentColor = e.target.dataset.color;
                colorPicker.value = this.currentColor;
                this.updateActiveColorSwatch();
            });
        });

        // Tools
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.currentTool = e.currentTarget.dataset.tool;
                this.updateActiveTool();
            });
        });

        // Save and download buttons
        document.getElementById('saveBtn').addEventListener('click', () => {
            this.showSaveModal();
        });

        document.getElementById('downloadBtn').addEventListener('click', () => {
            this.showSaveModal();
        });

        // Modal controls
        document.getElementById('modalClose').addEventListener('click', () => {
            this.hideSaveModal();
        });

        document.getElementById('savePNG').addEventListener('click', () => {
            this.saveAsPNG();
        });

        document.getElementById('savePDF').addEventListener('click', () => {
            this.saveAsPDF();
        });

        // Close modal on background click
        document.getElementById('saveModal').addEventListener('click', (e) => {
            if (e.target.id === 'saveModal') {
                this.hideSaveModal();
            }
        });
    }

    async loadDesigns() {
        // Sample SVG designs - in real app, these would come from the database
        this.designs = [
            {
                id: 1,
                name: 'Butterfly',
                svg: this.createButterflyDesign()
            },
            {
                id: 2,
                name: 'Flower',
                svg: this.createFlowerDesign()
            },
            {
                id: 3,
                name: 'Mandala',
                svg: this.createMandalaDesign()
            },
            {
                id: 4,
                name: 'Tree',
                svg: this.createTreeDesign()
            }
        ];

        this.renderDesignGrid();
        this.loadDesign(this.designs[0]);
    }

    renderDesignGrid() {
        const grid = document.getElementById('designGrid');
        grid.innerHTML = '';

        this.designs.forEach((design, index) => {
            const item = document.createElement('div');
            item.className = `design-item ${index === 0 ? 'active' : ''}`;
            item.innerHTML = design.svg;
            item.addEventListener('click', () => {
                this.loadDesign(design);
                this.updateActiveDesign(item);
            });
            grid.appendChild(item);
        });
    }

    loadDesign(design) {
        this.currentDesign = design;
        const svg = document.getElementById('coloringSvg');
        svg.innerHTML = design.svg;
        
        // Setup SVG paths for coloring
        this.setupColoringPaths();
        this.resetProgress();
    }

    setupColoringPaths() {
        const svg = document.getElementById('coloringSvg');
        const paths = svg.querySelectorAll('path');
        
        this.totalPaths = paths.length;
        this.coloredPaths.clear();

        paths.forEach((path, index) => {
            path.setAttribute('data-path-id', index);
            path.style.cursor = 'pointer';
            
            // Add hover effects
            path.addEventListener('mouseenter', () => {
                if (!path.classList.contains('colored')) {
                    path.style.fill = this.currentColor;
                    path.style.fillOpacity = '0.3';
                }
            });

            path.addEventListener('mouseleave', () => {
                if (!path.classList.contains('colored')) {
                    path.style.fill = 'transparent';
                    path.style.fillOpacity = '1';
                }
            });

            // Handle coloring
            path.addEventListener('click', () => {
                this.colorPath(path);
            });
        });
    }

    colorPath(path) {
        const pathId = path.getAttribute('data-path-id');
        
        switch (this.currentTool) {
            case 'brush':
            case 'fill':
                path.style.fill = this.currentColor;
                path.style.fillOpacity = '1';
                path.classList.add('colored');
                this.coloredPaths.add(pathId);
                break;
                
            case 'eraser':
                path.style.fill = 'transparent';
                path.style.fillOpacity = '1';
                path.classList.remove('colored');
                this.coloredPaths.delete(pathId);
                break;
        }
        
        this.updateProgress();
        this.addColoringAnimation(path);
    }

    addColoringAnimation(path) {
        path.style.transform = 'scale(1.1)';
        path.style.transition = 'transform 0.2s ease';
        
        setTimeout(() => {
            path.style.transform = 'scale(1)';
        }, 200);
    }

    updateProgress() {
        const progress = (this.coloredPaths.size / this.totalPaths) * 100;
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        
        progressFill.style.width = `${progress}%`;
        progressText.textContent = `${Math.round(progress)}% Complete`;
        
        if (progress === 100) {
            this.showCompletionAnimation();
        }
    }

    showCompletionAnimation() {
        const svg = document.getElementById('coloringSvg');
        svg.classList.add('pulse');
        
        setTimeout(() => {
            svg.classList.remove('pulse');
        }, 2000);
        
        // Show completion message
        this.showNotification('🎉 Congratulations! You completed the design!');
    }

    showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, #64ffda, #4ecdc4);
            color: #1a1a2e;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            font-weight: 500;
            z-index: 10001;
            animation: slideIn 0.5s ease;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    resetProgress() {
        this.coloredPaths.clear();
        this.updateProgress();
    }

    updateActiveColorSwatch() {
        document.querySelectorAll('.color-swatch').forEach(swatch => {
            swatch.classList.remove('active');
            if (swatch.dataset.color === this.currentColor) {
                swatch.classList.add('active');
            }
        });
    }

    updateActiveTool() {
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.tool === this.currentTool) {
                btn.classList.add('active');
            }
        });
    }

    updateActiveDesign(activeItem) {
        document.querySelectorAll('.design-item').forEach(item => {
            item.classList.remove('active');
        });
        activeItem.classList.add('active');
    }

    showSaveModal() {
        document.getElementById('saveModal').classList.add('active');
    }

    hideSaveModal() {
        document.getElementById('saveModal').classList.remove('active');
    }

    async saveAsPNG() {
        const svg = document.getElementById('coloringSvg');
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // Set canvas size
        canvas.width = 800;
        canvas.height = 800;
        
        // Create a white background
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        try {
            const svgData = new XMLSerializer().serializeToString(svg);
            const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
            const url = URL.createObjectURL(svgBlob);
            
            const img = new Image();
            img.onload = () => {
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                
                // Download the image
                canvas.toBlob((blob) => {
                    const link = document.createElement('a');
                    link.download = `coloring-${this.currentDesign.name}-${Date.now()}.png`;
                    link.href = URL.createObjectURL(blob);
                    link.click();
                    
                    URL.revokeObjectURL(link.href);
                    URL.revokeObjectURL(url);
                });
            };
            img.src = url;
        } catch (error) {
            console.error('Error saving PNG:', error);
            this.showNotification('Error saving image. Please try again.');
        }
        
        this.hideSaveModal();
    }

    async saveAsPDF() {
        try {
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF();
            
            const svg = document.getElementById('coloringSvg');
            const svgData = new XMLSerializer().serializeToString(svg);
            const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
            const url = URL.createObjectURL(svgBlob);
            
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = 800;
                canvas.height = 800;
                
                // White background
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                
                const imgData = canvas.toDataURL('image/png');
                pdf.addImage(imgData, 'PNG', 10, 10, 190, 190);
                pdf.save(`coloring-${this.currentDesign.name}-${Date.now()}.pdf`);
                
                URL.revokeObjectURL(url);
            };
            img.src = url;
        } catch (error) {
            console.error('Error saving PDF:', error);
            this.showNotification('Error saving PDF. Please try again.');
        }
        
        this.hideSaveModal();
    }

    hideLoadingScreen() {
        setTimeout(() => {
            document.getElementById('loadingScreen').classList.add('hidden');
        }, 1500);
    }

    // Sample SVG designs
    createButterflyDesign() {
        return `
            <g transform="translate(50, 50)">
                <path d="M100,150 Q80,120 60,100 Q40,80 30,60 Q25,40 35,25 Q50,15 70,25 Q85,35 90,50 Q95,35 110,25 Q130,15 145,25 Q155,40 150,60 Q140,80 120,100 Q100,120 100,150" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M100,150 Q120,120 140,100 Q160,80 170,60 Q175,40 165,25 Q150,15 130,25 Q115,35 110,50 Q105,35 90,25 Q70,15 55,25 Q45,40 50,60 Q60,80 80,100 Q100,120 100,150" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M100,50 L100,200" stroke="#333" stroke-width="3"/>
                <circle cx="100" cy="45" r="3" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M95,40 Q90,35 85,40" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M105,40 Q110,35 115,40" fill="transparent" stroke="#333" stroke-width="2"/>
            </g>
        `;
    }

    createFlowerDesign() {
        return `
            <g transform="translate(50, 50)">
                <circle cx="100" cy="100" r="15" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M100,85 Q85,70 70,85 Q85,100 100,85" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M115,100 Q130,85 115,70 Q100,85 115,100" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M100,115 Q115,130 130,115 Q115,100 100,115" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M85,100 Q70,115 85,130 Q100,115 85,100" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M107,93 Q122,78 107,63 Q92,78 107,93" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M107,107 Q122,122 137,107 Q122,92 107,107" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M93,107 Q78,122 63,107 Q78,92 93,107" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M93,93 Q78,78 93,63 Q108,78 93,93" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M100,115 L100,180 Q95,185 100,190 Q105,185 100,180" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M85,140 Q80,135 75,140 Q80,145 85,140" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M115,160 Q120,155 125,160 Q120,165 115,160" fill="transparent" stroke="#333" stroke-width="2"/>
            </g>
        `;
    }

    createMandalaDesign() {
        return `
            <g transform="translate(50, 50)">
                <circle cx="100" cy="100" r="80" fill="transparent" stroke="#333" stroke-width="2"/>
                <circle cx="100" cy="100" r="60" fill="transparent" stroke="#333" stroke-width="2"/>
                <circle cx="100" cy="100" r="40" fill="transparent" stroke="#333" stroke-width="2"/>
                <circle cx="100" cy="100" r="20" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M100,20 L100,180" stroke="#333" stroke-width="2"/>
                <path d="M20,100 L180,100" stroke="#333" stroke-width="2"/>
                <path d="M156,44 L44,156" stroke="#333" stroke-width="2"/>
                <path d="M44,44 L156,156" stroke="#333" stroke-width="2"/>
                <path d="M100,40 Q110,50 120,40 Q110,30 100,40" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M160,100 Q150,110 160,120 Q170,110 160,100" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M100,160 Q90,150 80,160 Q90,170 100,160" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M40,100 Q50,90 40,80 Q30,90 40,100" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M135,65 Q125,75 135,85 Q145,75 135,65" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M135,135 Q125,125 135,115 Q145,125 135,135" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M65,135 Q75,125 65,115 Q55,125 65,135" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M65,65 Q75,75 65,85 Q55,75 65,65" fill="transparent" stroke="#333" stroke-width="2"/>
            </g>
        `;
    }

    createTreeDesign() {
        return `
            <g transform="translate(50, 50)">
                <path d="M100,180 L100,120 Q95,115 100,110 Q105,115 100,120" fill="transparent" stroke="#333" stroke-width="3"/>
                <path d="M100,120 Q80,100 60,110 Q70,90 90,100 Q100,110 100,120" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M100,120 Q120,100 140,110 Q130,90 110,100 Q100,110 100,120" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M100,110 Q85,90 70,100 Q80,80 95,90 Q100,100 100,110" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M100,110 Q115,90 130,100 Q120,80 105,90 Q100,100 100,110" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M100,100 Q90,80 80,90 Q85,70 95,80 Q100,90 100,100" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M100,100 Q110,80 120,90 Q115,70 105,80 Q100,90 100,100" fill="transparent" stroke="#333" stroke-width="2"/>
                <circle cx="75" cy="95" r="3" fill="transparent" stroke="#333" stroke-width="2"/>
                <circle cx="125" cy="95" r="3" fill="transparent" stroke="#333" stroke-width="2"/>
                <circle cx="85" cy="75" r="2" fill="transparent" stroke="#333" stroke-width="2"/>
                <circle cx="115" cy="75" r="2" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M90,180 Q85,175 80,180 Q85,185 90,180" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M110,180 Q115,175 120,180 Q115,185 110,180" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M95,185 Q90,180 85,185 Q90,190 95,185" fill="transparent" stroke="#333" stroke-width="2"/>
                <path d="M105,185 Q110,180 115,185 Q110,190 105,185" fill="transparent" stroke="#333" stroke-width="2"/>
            </g>
        `;
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ColoringApp();
});

// Add CSS for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);