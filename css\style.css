/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #ffffff;
    overflow: hidden;
    height: 100vh;
}

/* App Container */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(10px);
}

/* Header */
.header {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem 2rem;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #64ffda;
}

.logo i {
    font-size: 2rem;
    background: linear-gradient(45deg, #64ffda, #ff6b6b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(45deg, #64ffda, #4ecdc4);
    color: #1a1a2e;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(100, 255, 218, 0.3);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 320px;
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(100, 255, 218, 0.3) transparent;
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(100, 255, 218, 0.3);
    border-radius: 3px;
}

.sidebar-section {
    margin-bottom: 2rem;
}

.sidebar-section h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    color: #64ffda;
    font-weight: 600;
}

/* Design Grid */
.design-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.75rem;
}

.design-item {
    aspect-ratio: 1;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.design-item:hover {
    border-color: #64ffda;
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(100, 255, 218, 0.2);
}

.design-item.active {
    border-color: #64ffda;
    background: rgba(100, 255, 218, 0.1);
}

.design-item svg {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Color Palette */
.color-palette {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.color-picker-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.color-picker-container input[type="color"] {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    background: none;
}

.color-picker-container label {
    font-size: 0.9rem;
    color: #ffffff;
}

.preset-colors {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
}

.color-swatch {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.color-swatch:hover {
    transform: scale(1.1);
    border-color: #64ffda;
}

.color-swatch.active {
    border-color: #64ffda;
    box-shadow: 0 0 15px rgba(100, 255, 218, 0.5);
}

/* Tools */
.tools {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.tool-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.tool-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: #64ffda;
}

.tool-btn.active {
    background: rgba(100, 255, 218, 0.1);
    border-color: #64ffda;
    color: #64ffda;
}

.tool-btn i {
    font-size: 1.1rem;
}

/* Zoom Controls */
.zoom-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.zoom-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
}

.zoom-btn {
    width: 100%;
    height: 40px;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.zoom-btn:hover {
    background: rgba(100, 255, 218, 0.2);
    color: #64ffda;
    transform: scale(1.05);
}

.zoom-level {
    text-align: center;
    color: #64ffda;
    font-weight: 500;
    font-size: 0.9rem;
}

.zoom-info {
    text-align: center;
}

.zoom-info small {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.75rem;
    line-height: 1.3;
}

/* Background Controls */
.background-controls {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.background-picker-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.background-picker-container input[type="color"] {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    background: none;
}

.background-picker-container label {
    font-size: 0.9rem;
    color: #ffffff;
}

.background-presets {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
}

.bg-preset {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.2);
    position: relative;
}

.bg-preset:hover {
    transform: scale(1.1);
    border-color: #64ffda;
}

.bg-preset.active {
    border-color: #64ffda;
    box-shadow: 0 0 15px rgba(100, 255, 218, 0.5);
}

/* Special styling for white and light backgrounds */
.bg-preset[data-color="#ffffff"],
.bg-preset[data-color="#f8f9fa"],
.bg-preset[data-color="#e9ecef"] {
    border-color: rgba(0, 0, 0, 0.3);
}

.bg-preset[data-color="#ffffff"]:hover,
.bg-preset[data-color="#f8f9fa"]:hover,
.bg-preset[data-color="#e9ecef"]:hover {
    border-color: #64ffda;
}

/* Progress */
.progress-container {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #64ffda, #4ecdc4);
    border-radius: 6px;
    transition: width 0.5s ease;
    width: 0%;
}

.progress-text {
    text-align: center;
    font-size: 0.9rem;
    color: #64ffda;
    font-weight: 500;
}

/* Canvas Area */
.canvas-area {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    background: radial-gradient(circle at center, rgba(100, 255, 218, 0.05) 0%, transparent 70%);
}

.canvas-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.canvas-wrapper {
    width: 90%;
    height: 90%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.coloring-svg {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 1;
}

.coloring-svg svg {
    max-width: 90%;
    max-height: 90%;
    width: auto;
    height: auto;
    filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.2));
    display: block;
}

.coloring-svg path,
.coloring-svg circle,
.coloring-svg rect,
.coloring-svg ellipse,
.coloring-svg polygon,
.coloring-svg polyline {
    stroke: #333333;
    stroke-width: 2;
    fill: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
}

.coloring-svg path:hover,
.coloring-svg circle:hover,
.coloring-svg rect:hover,
.coloring-svg ellipse:hover,
.coloring-svg polygon:hover,
.coloring-svg polyline:hover {
    stroke: #64ffda;
    stroke-width: 3;
}

.coloring-svg path.colored,
.coloring-svg circle.colored,
.coloring-svg rect.colored,
.coloring-svg ellipse.colored,
.coloring-svg polygon.colored,
.coloring-svg polyline.colored {
    stroke-width: 2;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 1;
    transition: opacity 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    text-align: center;
    color: #ffffff;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(100, 255, 218, 0.2);
    border-top: 4px solid #64ffda;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content p {
    font-size: 1.1rem;
    color: #64ffda;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.modal.active {
    opacity: 1;
    pointer-events: all;
}

.modal-content {
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    max-width: 400px;
    width: 90%;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal.active .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.modal-header h3 {
    color: #64ffda;
    font-size: 1.3rem;
}

.modal-close {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.save-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.save-option {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.save-option:hover {
    background: rgba(100, 255, 218, 0.1);
    border-color: #64ffda;
    transform: translateY(-2px);
}

.save-option i {
    font-size: 1.5rem;
    color: #64ffda;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 280px;
    }
    
    .header-content {
        padding: 0 1rem;
    }
    
    .canvas-area {
        padding: 1rem;
    }
    
    .preset-colors {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 480px) {
    .sidebar {
        width: 100%;
        position: fixed;
        top: 0;
        left: -100%;
        height: 100vh;
        z-index: 1001;
        transition: left 0.3s ease;
    }
    
    .sidebar.open {
        left: 0;
    }
    
    .main-content {
        flex-direction: column;
    }
    
    .canvas-area {
        flex: 1;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}