<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ColorCraft - Digital Coloring Book</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-palette"></i>
                    <span>ColorCraft</span>
                </div>
                <div class="header-actions">
                    <button class="btn btn-secondary" id="saveBtn">
                        <i class="fas fa-save"></i> Save
                    </button>
                    <button class="btn btn-primary" id="downloadBtn">
                        <i class="fas fa-download"></i> Download
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-section">
                    <h3><i class="fas fa-images"></i> Designs</h3>
                    <div class="design-grid" id="designGrid">
                        <!-- Designs will be loaded here -->
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3><i class="fas fa-palette"></i> Colors</h3>
                    <div class="color-palette">
                        <div class="color-picker-container">
                            <input type="color" id="colorPicker" value="#ff6b6b">
                            <label for="colorPicker">Custom Color</label>
                        </div>
                        <div class="preset-colors">
                            <div class="color-swatch" data-color="#ff6b6b" style="background: #ff6b6b;"></div>
                            <div class="color-swatch" data-color="#4ecdc4" style="background: #4ecdc4;"></div>
                            <div class="color-swatch" data-color="#45b7d1" style="background: #45b7d1;"></div>
                            <div class="color-swatch" data-color="#96ceb4" style="background: #96ceb4;"></div>
                            <div class="color-swatch" data-color="#feca57" style="background: #feca57;"></div>
                            <div class="color-swatch" data-color="#ff9ff3" style="background: #ff9ff3;"></div>
                            <div class="color-swatch" data-color="#54a0ff" style="background: #54a0ff;"></div>
                            <div class="color-swatch" data-color="#5f27cd" style="background: #5f27cd;"></div>
                            <div class="color-swatch" data-color="#00d2d3" style="background: #00d2d3;"></div>
                            <div class="color-swatch" data-color="#ff9f43" style="background: #ff9f43;"></div>
                            <div class="color-swatch" data-color="#10ac84" style="background: #10ac84;"></div>
                            <div class="color-swatch" data-color="#ee5a6f" style="background: #ee5a6f;"></div>
                        </div>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3><i class="fas fa-tools"></i> Tools</h3>
                    <div class="tools">
                        <button class="tool-btn active" data-tool="brush">
                            <i class="fas fa-paint-brush"></i>
                            <span>Brush</span>
                        </button>
                        <button class="tool-btn" data-tool="eraser">
                            <i class="fas fa-eraser"></i>
                            <span>Eraser</span>
                        </button>
                        <button class="tool-btn" data-tool="fill">
                            <i class="fas fa-fill-drip"></i>
                            <span>Fill</span>
                        </button>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3><i class="fas fa-chart-line"></i> Progress</h3>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div class="progress-text">
                            <span id="progressText">0% Complete</span>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Canvas Area -->
            <div class="canvas-area">
                <div class="canvas-container">
                    <div class="canvas-wrapper" id="canvasWrapper">
                        <svg id="coloringSvg" class="coloring-svg">
                            <!-- SVG content will be loaded here -->
                        </svg>
                    </div>
                </div>
            </div>
        </main>

        <!-- Loading Screen -->
        <div class="loading-screen" id="loadingScreen">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <p>Loading your coloring book...</p>
            </div>
        </div>

        <!-- Modal for Save/Download -->
        <div class="modal" id="saveModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Save Your Artwork</h3>
                    <button class="modal-close" id="modalClose">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="save-options">
                        <button class="save-option" id="savePNG">
                            <i class="fas fa-image"></i>
                            <span>Save as PNG</span>
                        </button>
                        <button class="save-option" id="savePDF">
                            <i class="fas fa-file-pdf"></i>
                            <span>Save as PDF</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="js/coloringApp.js"></script>
</body>
</html>