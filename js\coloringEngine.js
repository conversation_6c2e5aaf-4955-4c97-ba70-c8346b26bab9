class ColoringEngine {
    constructor() {
        this.currentColor = '#ff6b6b';
        this.currentTool = 'brush';
        this.coloredPaths = new Map(); // Store path ID and color
        this.totalPaths = 0;
        this.isColoring = false;
        this.colorHistory = [];
        this.maxHistorySize = 50;
        this.zoomLevel = 1;
        this.minZoom = 0.5;
        this.maxZoom = 4;
        this.panX = 0;
        this.panY = 0;
        this.isPanning = false;
        this.lastPanPoint = { x: 0, y: 0 };
        this.backgroundColor = '#ffffff';
        
        this.init();
    }

    init() {
        this.setupSVG();
        this.setupEventListeners();
        this.setupZoomAndPan();
        this.setupBackground();
        this.hideLoadingScreen();
        this.updateProgress();
    }

    setupSVG() {
        const svgContainer = document.getElementById('coloringSvg');
        const svgElement = svgContainer.querySelector('svg');
        
        if (!svgElement) {
            console.error('No SVG found in the container');
            return;
        }

        // Ensure SVG has proper attributes and visibility
        svgElement.setAttribute('width', '100%');
        svgElement.setAttribute('height', '100%');
        svgElement.style.maxWidth = '100%';
        svgElement.style.maxHeight = '100%';
        svgElement.style.display = 'block';
        svgElement.style.margin = 'auto';
        
        // Ensure SVG viewBox is set properly
        if (!svgElement.getAttribute('viewBox')) {
            // Try to get dimensions and set viewBox
            const width = svgElement.getAttribute('width') || '400';
            const height = svgElement.getAttribute('height') || '400';
            svgElement.setAttribute('viewBox', `0 0 ${width} ${height}`);
        }

        // Setup all paths for coloring
        this.setupColoringPaths();
        
        // Debug: Log SVG info
        console.log('SVG loaded:', svgElement);
        console.log('SVG viewBox:', svgElement.getAttribute('viewBox'));
        console.log('SVG paths found:', svgContainer.querySelectorAll('path, circle, rect, ellipse, polygon, polyline').length);
    }

    setupColoringPaths() {
        const svgContainer = document.getElementById('coloringSvg');
        const paths = svgContainer.querySelectorAll('path, circle, rect, ellipse, polygon, polyline');
        
        this.totalPaths = paths.length;
        this.coloredPaths.clear();

        paths.forEach((path, index) => {
            const pathId = `path-${index}`;
            path.setAttribute('data-path-id', pathId);
            path.style.cursor = 'pointer';
            path.style.transition = 'all 0.2s ease';
            
            // Store original attributes
            const originalFill = path.getAttribute('fill');
            const originalStroke = path.getAttribute('stroke');
            const originalStrokeWidth = path.getAttribute('stroke-width');
            
            // Set up for coloring - paths are borders, fill is the colorable area
            if (!originalStroke || originalStroke === 'none' || originalStroke === 'transparent') {
                path.setAttribute('stroke', '#333333');
                path.style.stroke = '#333333';
            }
            if (!originalStrokeWidth) {
                path.setAttribute('stroke-width', '2');
                path.style.strokeWidth = '2';
            }
            
            // Make fill transparent initially (this is what we'll color)
            path.setAttribute('fill', 'transparent');
            path.style.fill = 'transparent';
            path.style.fillOpacity = '1';
            
            // Store original values for reference
            path.setAttribute('data-original-fill', originalFill || 'transparent');
            path.setAttribute('data-original-stroke', originalStroke || '#333333');
            path.setAttribute('data-original-stroke-width', originalStrokeWidth || '2');

            // Add event listeners
            this.addPathEventListeners(path);
        });

        // Update total count display
        document.getElementById('totalCount').textContent = this.totalPaths;
    }

    addPathEventListeners(path) {
        // Mouse events
        path.addEventListener('mouseenter', (e) => this.handlePathHover(e, true));
        path.addEventListener('mouseleave', (e) => this.handlePathHover(e, false));
        path.addEventListener('click', (e) => this.handlePathClick(e));
        
        // Touch events for mobile
        path.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.handlePathClick(e);
        });

        // Prevent context menu
        path.addEventListener('contextmenu', (e) => e.preventDefault());
    }

    handlePathHover(event, isEntering) {
        if (this.isPanning) return; // Don't show hover effects while panning
        
        const path = event.target;
        const pathId = path.getAttribute('data-path-id');
        
        if (isEntering && !this.coloredPaths.has(pathId)) {
            // Show preview of current color in the fill area
            if (this.currentTool === 'brush' || this.currentTool === 'fill') {
                path.style.fill = this.currentColor;
                path.style.fillOpacity = '0.5';
                path.style.stroke = this.darkenColor(this.currentColor, 30);
                path.style.strokeWidth = '3';
            } else if (this.currentTool === 'eraser') {
                path.style.stroke = '#ff6b6b';
                path.style.strokeWidth = '4';
            }
        } else if (!isEntering && !this.coloredPaths.has(pathId)) {
            // Remove preview - restore to transparent fill
            path.style.fill = 'transparent';
            path.style.fillOpacity = '1';
            path.style.stroke = path.getAttribute('data-original-stroke');
            path.style.strokeWidth = path.getAttribute('data-original-stroke-width');
        }
    }

    handlePathClick(event) {
        if (this.isPanning) return; // Don't color while panning
        
        const path = event.target;
        const pathId = path.getAttribute('data-path-id');
        
        if (!pathId) return;

        // Save state for undo functionality
        this.saveState();

        switch (this.currentTool) {
            case 'brush':
            case 'fill':
                this.colorPath(path, pathId);
                break;
            case 'eraser':
                this.erasePath(path, pathId);
                break;
        }

        this.updateProgress();
        this.addColoringAnimation(path);
    }

    colorPath(path, pathId) {
        // Store the color
        this.coloredPaths.set(pathId, this.currentColor);
        
        // Apply the color to the fill area (inside the path)
        path.setAttribute('fill', this.currentColor);
        path.style.fill = this.currentColor;
        path.style.fillOpacity = '1';
        
        // Keep the stroke as border
        const borderColor = this.darkenColor(this.currentColor, 20);
        path.style.stroke = borderColor;
        path.style.strokeWidth = '2';
        path.classList.add('colored');

        // Add sparkle effect
        this.addSparkleEffect(path);
    }

    erasePath(path, pathId) {
        // Remove from colored paths
        this.coloredPaths.delete(pathId);
        
        // Reset to transparent fill
        path.setAttribute('fill', 'transparent');
        path.style.fill = 'transparent';
        path.style.fillOpacity = '1';
        path.style.stroke = path.getAttribute('data-original-stroke');
        path.style.strokeWidth = path.getAttribute('data-original-stroke-width');
        path.classList.remove('colored');
    }

    clearAllColors() {
        const svgContainer = document.getElementById('coloringSvg');
        const paths = svgContainer.querySelectorAll('[data-path-id]');
        
        this.saveState();
        
        paths.forEach(path => {
            const pathId = path.getAttribute('data-path-id');
            this.erasePath(path, pathId);
        });
        
        this.updateProgress();
        this.showNotification('All colors cleared!', 'info');
    }

    setupZoomAndPan() {
        const canvasWrapper = document.getElementById('canvasWrapper');
        const svgContainer = document.getElementById('coloringSvg');
        
        if (!canvasWrapper || !svgContainer) return;

        // Mouse wheel zoom
        canvasWrapper.addEventListener('wheel', (e) => {
            e.preventDefault();
            
            const rect = canvasWrapper.getBoundingClientRect();
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
            const newZoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.zoomLevel * zoomFactor));
            
            if (newZoom !== this.zoomLevel) {
                this.zoomLevel = newZoom;
                this.updateTransform();
                this.updateZoomDisplay();
            }
        });

        // Pan functionality
        let startX, startY;
        
        canvasWrapper.addEventListener('mousedown', (e) => {
            if (e.button === 1 || e.ctrlKey) { // Middle mouse or Ctrl+click for panning
                e.preventDefault();
                this.isPanning = true;
                startX = e.clientX - this.panX;
                startY = e.clientY - this.panY;
                canvasWrapper.style.cursor = 'grabbing';
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (this.isPanning) {
                this.panX = e.clientX - startX;
                this.panY = e.clientY - startY;
                this.updateTransform();
            }
        });

        document.addEventListener('mouseup', () => {
            if (this.isPanning) {
                this.isPanning = false;
                canvasWrapper.style.cursor = 'default';
            }
        });

        // Touch events for mobile zoom and pan
        let lastTouchDistance = 0;
        let lastTouchCenter = { x: 0, y: 0 };

        canvasWrapper.addEventListener('touchstart', (e) => {
            if (e.touches.length === 2) {
                e.preventDefault();
                const touch1 = e.touches[0];
                const touch2 = e.touches[1];
                
                lastTouchDistance = Math.hypot(
                    touch2.clientX - touch1.clientX,
                    touch2.clientY - touch1.clientY
                );
                
                lastTouchCenter = {
                    x: (touch1.clientX + touch2.clientX) / 2,
                    y: (touch1.clientY + touch2.clientY) / 2
                };
            }
        });

        canvasWrapper.addEventListener('touchmove', (e) => {
            if (e.touches.length === 2) {
                e.preventDefault();
                const touch1 = e.touches[0];
                const touch2 = e.touches[1];
                
                const currentDistance = Math.hypot(
                    touch2.clientX - touch1.clientX,
                    touch2.clientY - touch1.clientY
                );
                
                const currentCenter = {
                    x: (touch1.clientX + touch2.clientX) / 2,
                    y: (touch1.clientY + touch2.clientY) / 2
                };

                // Zoom
                if (lastTouchDistance > 0) {
                    const zoomFactor = currentDistance / lastTouchDistance;
                    const newZoom = Math.max(this.minZoom, Math.min(this.maxZoom, this.zoomLevel * zoomFactor));
                    
                    if (newZoom !== this.zoomLevel) {
                        this.zoomLevel = newZoom;
                        this.updateTransform();
                        this.updateZoomDisplay();
                    }
                }

                // Pan
                this.panX += currentCenter.x - lastTouchCenter.x;
                this.panY += currentCenter.y - lastTouchCenter.y;
                this.updateTransform();

                lastTouchDistance = currentDistance;
                lastTouchCenter = currentCenter;
            }
        });
    }

    updateTransform() {
        const svgContainer = document.getElementById('coloringSvg');
        if (svgContainer) {
            svgContainer.style.transform = `translate(${this.panX}px, ${this.panY}px) scale(${this.zoomLevel})`;
        }
    }

    updateZoomDisplay() {
        // Update zoom level display if it exists
        const zoomDisplay = document.getElementById('zoomLevel');
        if (zoomDisplay) {
            zoomDisplay.textContent = `${Math.round(this.zoomLevel * 100)}%`;
        }
    }

    setupBackground() {
        const canvasContainer = document.querySelector('.canvas-container');
        if (canvasContainer) {
            canvasContainer.style.backgroundColor = this.backgroundColor;
        }
    }

    changeBackgroundColor(color) {
        this.backgroundColor = color;
        const canvasContainer = document.querySelector('.canvas-container');
        if (canvasContainer) {
            canvasContainer.style.backgroundColor = color;
        }
    }

    resetZoomAndPan() {
        this.zoomLevel = 1;
        this.panX = 0;
        this.panY = 0;
        this.updateTransform();
        this.updateZoomDisplay();
    }

    zoomIn() {
        const newZoom = Math.min(this.maxZoom, this.zoomLevel * 1.2);
        if (newZoom !== this.zoomLevel) {
            this.zoomLevel = newZoom;
            this.updateTransform();
            this.updateZoomDisplay();
        }
    }

    zoomOut() {
        const newZoom = Math.max(this.minZoom, this.zoomLevel * 0.8);
        if (newZoom !== this.zoomLevel) {
            this.zoomLevel = newZoom;
            this.updateTransform();
            this.updateZoomDisplay();
        }
    }

    fitToScreen() {
        const canvasWrapper = document.getElementById('canvasWrapper');
        const svgContainer = document.getElementById('coloringSvg');
        const svgElement = svgContainer.querySelector('svg');
        
        if (!canvasWrapper || !svgElement) return;

        const wrapperRect = canvasWrapper.getBoundingClientRect();
        const svgRect = svgElement.getBoundingClientRect();
        
        const scaleX = (wrapperRect.width * 0.9) / svgRect.width;
        const scaleY = (wrapperRect.height * 0.9) / svgRect.height;
        
        this.zoomLevel = Math.min(scaleX, scaleY, this.maxZoom);
        this.panX = 0;
        this.panY = 0;
        
        this.updateTransform();
        this.updateZoomDisplay();
    }

    addColoringAnimation(path) {
        const originalTransform = path.style.transform;
        path.style.transform = `${originalTransform} scale(1.1)`;
        
        setTimeout(() => {
            path.style.transform = originalTransform;
        }, 200);
    }

    addSparkleEffect(path) {
        const rect = path.getBoundingClientRect();
        const sparkle = document.createElement('div');
        
        sparkle.style.cssText = `
            position: fixed;
            left: ${rect.left + rect.width/2}px;
            top: ${rect.top + rect.height/2}px;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, ${this.currentColor}, transparent);
            border-radius: 50%;
            pointer-events: none;
            z-index: 1000;
            animation: sparkle 0.6s ease-out forwards;
        `;
        
        document.body.appendChild(sparkle);
        
        setTimeout(() => {
            sparkle.remove();
        }, 600);
    }

    updateProgress() {
        const coloredCount = this.coloredPaths.size;
        const progress = this.totalPaths > 0 ? (coloredCount / this.totalPaths) * 100 : 0;
        
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const coloredCountEl = document.getElementById('coloredCount');
        
        if (progressFill) progressFill.style.width = `${progress}%`;
        if (progressText) progressText.textContent = `${Math.round(progress)}% Complete`;
        if (coloredCountEl) coloredCountEl.textContent = coloredCount;
        
        // Check for completion
        if (progress === 100) {
            this.showCompletionAnimation();
        }
    }

    showCompletionAnimation() {
        const svgContainer = document.getElementById('coloringSvg');
        svgContainer.classList.add('pulse');
        
        // Create confetti effect
        this.createConfetti();
        
        setTimeout(() => {
            svgContainer.classList.remove('pulse');
        }, 2000);
        
        this.showNotification('🎉 Congratulations! You completed the design!', 'success');
    }

    createConfetti() {
        const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3'];
        
        for (let i = 0; i < 50; i++) {
            setTimeout(() => {
                const confetti = document.createElement('div');
                confetti.style.cssText = `
                    position: fixed;
                    left: ${Math.random() * 100}vw;
                    top: -10px;
                    width: 10px;
                    height: 10px;
                    background: ${colors[Math.floor(Math.random() * colors.length)]};
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 10000;
                    animation: confetti-fall 3s linear forwards;
                `;
                
                document.body.appendChild(confetti);
                
                setTimeout(() => {
                    confetti.remove();
                }, 3000);
            }, i * 100);
        }
    }

    setupEventListeners() {
        // Color picker
        const colorPicker = document.getElementById('colorPicker');
        if (colorPicker) {
            colorPicker.addEventListener('change', (e) => {
                this.currentColor = e.target.value;
                this.updateActiveColorSwatch();
            });
        }

        // Color swatches
        document.querySelectorAll('.color-swatch').forEach(swatch => {
            swatch.addEventListener('click', (e) => {
                this.currentColor = e.target.dataset.color;
                if (colorPicker) colorPicker.value = this.currentColor;
                this.updateActiveColorSwatch();
            });
        });

        // Tools
        document.querySelectorAll('.tool-btn[data-tool]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.currentTool = e.currentTarget.dataset.tool;
                this.updateActiveTool();
            });
        });

        // Zoom controls
        const zoomInBtn = document.getElementById('zoomInBtn');
        const zoomOutBtn = document.getElementById('zoomOutBtn');
        const resetZoomBtn = document.getElementById('resetZoomBtn');
        const fitScreenBtn = document.getElementById('fitScreenBtn');

        if (zoomInBtn) zoomInBtn.addEventListener('click', () => this.zoomIn());
        if (zoomOutBtn) zoomOutBtn.addEventListener('click', () => this.zoomOut());
        if (resetZoomBtn) resetZoomBtn.addEventListener('click', () => this.resetZoomAndPan());
        if (fitScreenBtn) fitScreenBtn.addEventListener('click', () => this.fitToScreen());

        // Background color controls
        const backgroundPicker = document.getElementById('backgroundPicker');
        if (backgroundPicker) {
            backgroundPicker.addEventListener('change', (e) => {
                this.changeBackgroundColor(e.target.value);
                this.updateActiveBackgroundPreset();
            });
        }

        // Background presets
        document.querySelectorAll('.bg-preset').forEach(preset => {
            preset.addEventListener('click', (e) => {
                const color = e.target.dataset.color;
                this.changeBackgroundColor(color);
                if (backgroundPicker) backgroundPicker.value = color;
                this.updateActiveBackgroundPreset();
            });
        });

        // Save and download buttons
        const saveBtn = document.getElementById('saveBtn');
        const downloadBtn = document.getElementById('downloadBtn');
        
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.showSaveModal());
        }
        
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => this.showSaveModal());
        }

        // Modal controls
        const modalClose = document.getElementById('modalClose');
        const savePNG = document.getElementById('savePNG');
        const savePDF = document.getElementById('savePDF');
        const saveModal = document.getElementById('saveModal');
        
        if (modalClose) {
            modalClose.addEventListener('click', () => this.hideSaveModal());
        }
        
        if (savePNG) {
            savePNG.addEventListener('click', () => this.saveAsPNG());
        }
        
        if (savePDF) {
            savePDF.addEventListener('click', () => this.saveAsPDF());
        }
        
        if (saveModal) {
            saveModal.addEventListener('click', (e) => {
                if (e.target.id === 'saveModal') {
                    this.hideSaveModal();
                }
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 's':
                        e.preventDefault();
                        this.showSaveModal();
                        break;
                    case 'z':
                        e.preventDefault();
                        this.undo();
                        break;
                    case '=':
                    case '+':
                        e.preventDefault();
                        this.zoomIn();
                        break;
                    case '-':
                        e.preventDefault();
                        this.zoomOut();
                        break;
                    case '0':
                        e.preventDefault();
                        this.resetZoomAndPan();
                        break;
                }
            }
        });
    }

    updateActiveColorSwatch() {
        document.querySelectorAll('.color-swatch').forEach(swatch => {
            swatch.classList.remove('active');
            if (swatch.dataset.color === this.currentColor) {
                swatch.classList.add('active');
            }
        });
    }

    updateActiveTool() {
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.tool === this.currentTool) {
                btn.classList.add('active');
            }
        });
    }

    updateActiveBackgroundPreset() {
        document.querySelectorAll('.bg-preset').forEach(preset => {
            preset.classList.remove('active');
            if (preset.dataset.color === this.backgroundColor) {
                preset.classList.add('active');
            }
        });
    }

    showSaveModal() {
        const modal = document.getElementById('saveModal');
        if (modal) {
            modal.classList.add('active');
        }
    }

    hideSaveModal() {
        const modal = document.getElementById('saveModal');
        if (modal) {
            modal.classList.remove('active');
        }
    }

    async saveAsPNG() {
        try {
            const svgContainer = document.getElementById('coloringSvg');
            const svgElement = svgContainer.querySelector('svg');
            
            if (!svgElement) {
                throw new Error('No SVG found');
            }

            // Create a canvas
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // Set high resolution
            const scale = 2;
            canvas.width = 800 * scale;
            canvas.height = 800 * scale;
            ctx.scale(scale, scale);
            
            // Background color
            ctx.fillStyle = this.backgroundColor;
            ctx.fillRect(0, 0, 800, 800);
            
            // Convert SVG to image
            const svgData = new XMLSerializer().serializeToString(svgElement);
            const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
            const url = URL.createObjectURL(svgBlob);
            
            const img = new Image();
            img.onload = () => {
                ctx.drawImage(img, 0, 0, 800, 800);
                
                // Download
                canvas.toBlob((blob) => {
                    const link = document.createElement('a');
                    link.download = `${designData.name}-colored-${Date.now()}.png`;
                    link.href = URL.createObjectURL(blob);
                    link.click();
                    
                    URL.revokeObjectURL(link.href);
                    URL.revokeObjectURL(url);
                    
                    this.showNotification('PNG saved successfully!', 'success');
                });
            };
            
            img.onerror = () => {
                throw new Error('Failed to load SVG image');
            };
            
            img.src = url;
            
        } catch (error) {
            console.error('Error saving PNG:', error);
            this.showNotification('Error saving PNG. Please try again.', 'error');
        }
        
        this.hideSaveModal();
    }

    async saveAsPDF() {
        try {
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF();
            
            const svgContainer = document.getElementById('coloringSvg');
            const svgElement = svgContainer.querySelector('svg');
            
            if (!svgElement) {
                throw new Error('No SVG found');
            }
            
            const svgData = new XMLSerializer().serializeToString(svgElement);
            const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
            const url = URL.createObjectURL(svgBlob);
            
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = 800;
                canvas.height = 800;
                
                // Background color
                ctx.fillStyle = this.backgroundColor;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                
                const imgData = canvas.toDataURL('image/png');
                pdf.addImage(imgData, 'PNG', 10, 10, 190, 190);
                pdf.save(`${designData.name}-colored-${Date.now()}.pdf`);
                
                URL.revokeObjectURL(url);
                this.showNotification('PDF saved successfully!', 'success');
            };
            
            img.onerror = () => {
                throw new Error('Failed to load SVG image');
            };
            
            img.src = url;
            
        } catch (error) {
            console.error('Error saving PDF:', error);
            this.showNotification('Error saving PDF. Please try again.', 'error');
        }
        
        this.hideSaveModal();
    }

    saveState() {
        const state = new Map(this.coloredPaths);
        this.colorHistory.push(state);
        
        if (this.colorHistory.length > this.maxHistorySize) {
            this.colorHistory.shift();
        }
    }

    undo() {
        if (this.colorHistory.length === 0) return;
        
        const previousState = this.colorHistory.pop();
        this.coloredPaths = new Map(previousState);
        
        // Restore visual state
        const svgContainer = document.getElementById('coloringSvg');
        const paths = svgContainer.querySelectorAll('[data-path-id]');
        
        paths.forEach(path => {
            const pathId = path.getAttribute('data-path-id');
            if (this.coloredPaths.has(pathId)) {
                const color = this.coloredPaths.get(pathId);
                this.colorPath(path, pathId);
            } else {
                this.erasePath(path, pathId);
            }
        });
        
        this.updateProgress();
        this.showNotification('Undo successful!', 'info');
    }

    darkenColor(color, percent) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) - amt;
        const G = (num >> 8 & 0x00FF) - amt;
        const B = (num & 0x0000FF) - amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        const icon = type === 'success' ? 'check-circle' : 
                    type === 'error' ? 'exclamation-circle' : 'info-circle';
        
        notification.innerHTML = `
            <i class="fas fa-${icon}"></i>
            <span>${message}</span>
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? 'rgba(76, 175, 80, 0.9)' : 
                        type === 'error' ? 'rgba(244, 67, 54, 0.9)' : 
                        'rgba(33, 150, 243, 0.9)'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            font-weight: 500;
            z-index: 10001;
            animation: slideInRight 0.5s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.5s ease';
            setTimeout(() => {
                notification.remove();
            }, 500);
        }, 3000);
    }

    hideLoadingScreen() {
        setTimeout(() => {
            const loadingScreen = document.getElementById('loadingScreen');
            if (loadingScreen) {
                loadingScreen.classList.add('hidden');
            }
        }, 1000);
    }
}

// Initialize the coloring engine
let coloringApp;
document.addEventListener('DOMContentLoaded', () => {
    coloringApp = new ColoringEngine();
});

// Add required CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes sparkle {
        0% { transform: scale(0) rotate(0deg); opacity: 1; }
        50% { transform: scale(1.5) rotate(180deg); opacity: 0.8; }
        100% { transform: scale(0) rotate(360deg); opacity: 0; }
    }
    
    @keyframes confetti-fall {
        0% { transform: translateY(-10px) rotate(0deg); opacity: 1; }
        100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
    }
    
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .progress-stats {
        display: flex;
        justify-content: space-between;
        margin-top: 0.75rem;
        font-size: 0.85rem;
    }
    
    .stat {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
    }
    
    .stat-label {
        color: rgba(255, 255, 255, 0.6);
    }
    
    .stat-value {
        color: #64ffda;
        font-weight: 600;
    }
    
    .design-title {
        flex: 1;
        text-align: center;
    }
    
    .design-title h2 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 0.25rem;
    }
    
    .design-title p {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
    }
    
    .canvas-container {
        position: relative;
        overflow: hidden;
    }
    
    .canvas-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;
        cursor: default;
    }
    
    #coloringSvg {
        transform-origin: center center;
        transition: transform 0.1s ease-out;
    }
`;
document.head.appendChild(style);