<?php
include 'admin/config.php';

// Get design and collection IDs from URL
$design_id = isset($_GET['design']) ? (int)$_GET['design'] : 0;
$collection_id = isset($_GET['collection']) ? (int)$_GET['collection'] : 0;

if ($design_id <= 0) {
    header('Location: index.html');
    exit;
}

// Get design details
$design_sql = "SELECT d.*, c.name as collection_name FROM designs d JOIN collections c ON d.collection_id = c.id WHERE d.id = $design_id";
$design_result = mysqli_query($conn, $design_sql);

if (!$design_result || mysqli_num_rows($design_result) == 0) {
    header('Location: index.html');
    exit;
}

$design = mysqli_fetch_assoc($design_result);

// Read SVG content
$svg_content = '';
if (file_exists("uploads/" . $design['svg_file'])) {
    $svg_content = file_get_contents("uploads/" . $design['svg_file']);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($design['name']); ?> - ColorCraft</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-palette"></i>
                    <span>ColorCraft</span>
                </div>
                <div class="design-title">
                    <h2><?php echo htmlspecialchars($design['name']); ?></h2>
                    <p>from <?php echo htmlspecialchars($design['collection_name']); ?></p>
                </div>
                <div class="header-actions">
                    <button class="btn btn-secondary" onclick="goBack()">
                        <i class="fas fa-arrow-left"></i> Back
                    </button>
                    <button class="btn btn-secondary" id="saveBtn">
                        <i class="fas fa-save"></i> Save
                    </button>
                    <button class="btn btn-primary" id="downloadBtn">
                        <i class="fas fa-download"></i> Download
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-section">
                    <h3><i class="fas fa-palette"></i> Colors</h3>
                    <div class="color-palette">
                        <div class="color-picker-container">
                            <input type="color" id="colorPicker" value="#ff6b6b">
                            <label for="colorPicker">Custom Color</label>
                        </div>
                        <div class="preset-colors">
                            <div class="color-swatch active" data-color="#ff6b6b" style="background: #ff6b6b;"></div>
                            <div class="color-swatch" data-color="#4ecdc4" style="background: #4ecdc4;"></div>
                            <div class="color-swatch" data-color="#45b7d1" style="background: #45b7d1;"></div>
                            <div class="color-swatch" data-color="#96ceb4" style="background: #96ceb4;"></div>
                            <div class="color-swatch" data-color="#feca57" style="background: #feca57;"></div>
                            <div class="color-swatch" data-color="#ff9ff3" style="background: #ff9ff3;"></div>
                            <div class="color-swatch" data-color="#54a0ff" style="background: #54a0ff;"></div>
                            <div class="color-swatch" data-color="#5f27cd" style="background: #5f27cd;"></div>
                            <div class="color-swatch" data-color="#00d2d3" style="background: #00d2d3;"></div>
                            <div class="color-swatch" data-color="#ff9f43" style="background: #ff9f43;"></div>
                            <div class="color-swatch" data-color="#10ac84" style="background: #10ac84;"></div>
                            <div class="color-swatch" data-color="#ee5a6f" style="background: #ee5a6f;"></div>
                            <div class="color-swatch" data-color="#ffffff" style="background: #ffffff;"></div>
                            <div class="color-swatch" data-color="#2c2c54" style="background: #2c2c54;"></div>
                            <div class="color-swatch" data-color="#40407a" style="background: #40407a;"></div>
                            <div class="color-swatch" data-color="#706fd3" style="background: #706fd3;"></div>
                        </div>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3><i class="fas fa-tools"></i> Tools</h3>
                    <div class="tools">
                        <button class="tool-btn active" data-tool="brush">
                            <i class="fas fa-paint-brush"></i>
                            <span>Brush</span>
                        </button>
                        <button class="tool-btn" data-tool="eraser">
                            <i class="fas fa-eraser"></i>
                            <span>Eraser</span>
                        </button>
                        <button class="tool-btn" data-tool="fill">
                            <i class="fas fa-fill-drip"></i>
                            <span>Fill</span>
                        </button>
                        <button class="tool-btn" data-tool="clear" onclick="clearAll()">
                            <i class="fas fa-trash"></i>
                            <span>Clear All</span>
                        </button>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3><i class="fas fa-search-plus"></i> Zoom & View</h3>
                    <div class="zoom-controls">
                        <div class="zoom-buttons">
                            <button class="zoom-btn" id="zoomInBtn" title="Zoom In (Ctrl++)">
                                <i class="fas fa-plus"></i>
                            </button>
                            <button class="zoom-btn" id="zoomOutBtn" title="Zoom Out (Ctrl+-)">
                                <i class="fas fa-minus"></i>
                            </button>
                            <button class="zoom-btn" id="resetZoomBtn" title="Reset Zoom (Ctrl+0)">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </button>
                            <button class="zoom-btn" id="fitScreenBtn" title="Fit to Screen">
                                <i class="fas fa-compress-arrows-alt"></i>
                            </button>
                        </div>
                        <div class="zoom-level">
                            <span>Zoom: <span id="zoomLevel">100%</span></span>
                        </div>
                        <div class="zoom-info">
                            <small>Mouse wheel to zoom, Ctrl+drag to pan</small>
                        </div>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3><i class="fas fa-palette"></i> Background</h3>
                    <div class="background-controls">
                        <div class="background-picker-container">
                            <input type="color" id="backgroundPicker" value="#ffffff">
                            <label for="backgroundPicker">Background Color</label>
                        </div>
                        <div class="background-presets">
                            <div class="bg-preset" data-color="#ffffff" style="background: #ffffff;" title="White"></div>
                            <div class="bg-preset" data-color="#f8f9fa" style="background: #f8f9fa;" title="Light Gray"></div>
                            <div class="bg-preset" data-color="#e9ecef" style="background: #e9ecef;" title="Gray"></div>
                            <div class="bg-preset" data-color="#000000" style="background: #000000;" title="Black"></div>
                            <div class="bg-preset" data-color="#ffeaa7" style="background: #ffeaa7;" title="Light Yellow"></div>
                            <div class="bg-preset" data-color="#fab1a0" style="background: #fab1a0;" title="Light Orange"></div>
                            <div class="bg-preset" data-color="#fd79a8" style="background: #fd79a8;" title="Light Pink"></div>
                            <div class="bg-preset" data-color="#a29bfe" style="background: #a29bfe;" title="Light Purple"></div>
                        </div>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3><i class="fas fa-chart-line"></i> Progress</h3>
                    <div class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div class="progress-text">
                            <span id="progressText">0% Complete</span>
                        </div>
                        <div class="progress-stats">
                            <div class="stat">
                                <span class="stat-label">Colored:</span>
                                <span class="stat-value" id="coloredCount">0</span>
                            </div>
                            <div class="stat">
                                <span class="stat-label">Total:</span>
                                <span class="stat-value" id="totalCount">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Canvas Area -->
            <div class="canvas-area">
                <div class="canvas-container">
                    <div class="canvas-wrapper" id="canvasWrapper">
                        <div id="coloringSvg" class="coloring-svg">
                            <?php echo $svg_content; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Loading Screen -->
        <div class="loading-screen" id="loadingScreen">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <p>Preparing your coloring canvas...</p>
            </div>
        </div>

        <!-- Modal for Save/Download -->
        <div class="modal" id="saveModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Save Your Artwork</h3>
                    <button class="modal-close" id="modalClose">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="save-options">
                        <button class="save-option" id="savePNG">
                            <i class="fas fa-image"></i>
                            <span>Save as PNG</span>
                        </button>
                        <button class="save-option" id="savePDF">
                            <i class="fas fa-file-pdf"></i>
                            <span>Save as PDF</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script>
        // Design data
        const designData = {
            id: <?php echo $design_id; ?>,
            name: '<?php echo addslashes($design['name']); ?>',
            collection_id: <?php echo $collection_id; ?>
        };
        
        function goBack() {
            window.location.href = `collection.php?id=${designData.collection_id}`;
        }
        
        function clearAll() {
            if (confirm('Are you sure you want to clear all colors? This action cannot be undone.')) {
                coloringApp.clearAllColors();
            }
        }
    </script>
    <script src="js/coloringEngine.js"></script>
</body>
</html>